// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * @title Dice
 * @dev A contract for generating random dice rolls
 */
contract Dice {
    // Nonce for additional randomness
    uint256 private _nonce;

    // Owner of the contract
    address private _owner;

    // Events
    event DiceRolled(address indexed roller, uint8 value);

    /**
     * @dev Constructor
     */
    constructor() {
        _owner = msg.sender;
        _nonce = 0;
    }

    /**
     * @dev Roll the dice
     * @return A random number between 1 and 6
     */
    function rollDice() public returns (uint8) {
        // Increment nonce for additional randomness
        _nonce++;

        // Generate random number
        uint256 randomNumber = uint256(
            keccak256(
                abi.encodePacked(
                    block.timestamp,
                    block.prevrandao,
                    msg.sender,
                    _nonce
                )
            )
        );

        // Get dice value (1-6)
        uint8 diceValue = uint8((randomNumber % 6) + 1);

        emit DiceRolled(msg.sender, diceValue);

        return diceValue;
    }

    /**
     * @dev Get the current nonce
     * @return The current nonce
     */
    function getNonce() external view returns (uint256) {
        return _nonce;
    }

    /**
     * @dev Reset the nonce (only owner)
     */
    function resetNonce() external {
        require(msg.sender == _owner, "Only owner can reset nonce");
        _nonce = 0;
    }
}
