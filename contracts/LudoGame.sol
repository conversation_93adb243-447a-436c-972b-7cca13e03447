// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "./Dice.sol";

/**
 * @title LudoGame
 * @dev A smart contract for managing Ludo game state and logic
 */
contract LudoGame {
    // Game status enum
    enum GameStatus {
        WAITING,
        ACTIVE,
        COMPLETED,
        CANCELLED
    }

    // Player status enum
    enum PlayerStatus {
        NOT_JOINED,
        JOINED,
        PLAYING,
        FINISHED
    }

    // Player struct to store player information
    struct Player {
        address playerAddress;
        uint8 playerIndex; // 0-3 for red, green, blue, yellow
        PlayerStatus status;
        uint8 piecesHome; // Number of pieces that reached home (0-4)
        uint256 joinedAt;
    }

    // Game struct to store game information
    struct GameState {
        uint256 gameId;
        address creator;
        uint256 entryFee;
        uint256 prizePool;
        uint8 maxPlayers; // 2-4 players
        uint8 currentPlayers;
        uint8 currentTurn; // Index of the current player (0-3)
        GameStatus status;
        uint256 createdAt;
        uint256 startedAt;
        uint256 endedAt;
        address winner;
    }

    // Piece position struct
    struct PiecePosition {
        bool isOut; // Whether the piece is out of the starting area
        uint8 position; // 0-51 for the main track, 52-57 for home track
        bool isHome; // Whether the piece has reached home
    }

    // Game ID counter
    uint256 private _gameIdCounter;

    // Mapping from game ID to game state
    mapping(uint256 => GameState) public games;

    // Mapping from game ID to players
    mapping(uint256 => mapping(uint8 => Player)) public gamePlayers;

    // Mapping from game ID to player index to piece positions
    mapping(uint256 => mapping(uint8 => mapping(uint8 => PiecePosition)))
        public gamePieces;

    // Dice contract for random number generation
    Dice private _dice;

    // Events
    event GameCreated(
        uint256 indexed gameId,
        address indexed creator,
        uint256 entryFee
    );
    event PlayerJoined(
        uint256 indexed gameId,
        address indexed player,
        uint8 playerIndex
    );
    event GameStarted(uint256 indexed gameId);
    event DiceRolled(
        uint256 indexed gameId,
        address indexed player,
        uint8 diceValue
    );
    event PieceMoved(
        uint256 indexed gameId,
        address indexed player,
        uint8 pieceIndex,
        uint8 fromPosition,
        uint8 toPosition
    );
    event PieceCaptured(
        uint256 indexed gameId,
        address indexed capturer,
        address captured,
        uint8 pieceIndex
    );
    event PlayerFinished(
        uint256 indexed gameId,
        address indexed player,
        uint8 position
    );
    event GameCompleted(
        uint256 indexed gameId,
        address indexed winner,
        uint256 prize
    );
    event GameCancelled(uint256 indexed gameId);

    // Constructor
    constructor(address diceContractAddress) {
        _dice = Dice(diceContractAddress);
        _gameIdCounter = 1;
    }

    /**
     * @dev Create a new game
     * @param entryFee The entry fee for the game in wei
     * @param maxPlayers The maximum number of players (2-4)
     * @return The ID of the created game
     */
    function createGame(
        uint256 entryFee,
        uint8 maxPlayers
    ) external payable returns (uint256) {
        require(
            maxPlayers >= 2 && maxPlayers <= 4,
            "Player count must be between 2 and 4"
        );
        require(msg.value == entryFee, "Must send exact entry fee");

        uint256 gameId = _gameIdCounter++;

        games[gameId] = GameState({
            gameId: gameId,
            creator: msg.sender,
            entryFee: entryFee,
            prizePool: msg.value, // Initial prize pool is the creator's entry fee
            maxPlayers: maxPlayers,
            currentPlayers: 1, // Creator is the first player
            currentTurn: 0, // First player starts
            status: GameStatus.WAITING,
            createdAt: block.timestamp,
            startedAt: 0,
            endedAt: 0,
            winner: address(0)
        });

        // Add creator as the first player (red)
        gamePlayers[gameId][0] = Player({
            playerAddress: msg.sender,
            playerIndex: 0,
            status: PlayerStatus.JOINED,
            piecesHome: 0,
            joinedAt: block.timestamp
        });

        // Initialize pieces for the creator
        for (uint8 i = 0; i < 4; i++) {
            gamePieces[gameId][0][i] = PiecePosition({
                isOut: false,
                position: 0,
                isHome: false
            });
        }

        emit GameCreated(gameId, msg.sender, entryFee);
        emit PlayerJoined(gameId, msg.sender, 0);

        return gameId;
    }

    /**
     * @dev Join an existing game
     * @param gameId The ID of the game to join
     */
    function joinGame(uint256 gameId) external payable {
        GameState storage game = games[gameId];

        require(game.gameId == gameId, "Game does not exist");
        require(
            game.status == GameStatus.WAITING,
            "Game is not in waiting state"
        );
        require(game.currentPlayers < game.maxPlayers, "Game is full");
        require(msg.value == game.entryFee, "Must send exact entry fee");

        // Check if player is already in the game
        for (uint8 i = 0; i < game.currentPlayers; i++) {
            require(
                gamePlayers[gameId][i].playerAddress != msg.sender,
                "Already joined this game"
            );
        }

        uint8 playerIndex = game.currentPlayers;

        // Add player to the game
        gamePlayers[gameId][playerIndex] = Player({
            playerAddress: msg.sender,
            playerIndex: playerIndex,
            status: PlayerStatus.JOINED,
            piecesHome: 0,
            joinedAt: block.timestamp
        });

        // Initialize pieces for the player
        for (uint8 i = 0; i < 4; i++) {
            gamePieces[gameId][playerIndex][i] = PiecePosition({
                isOut: false,
                position: 0,
                isHome: false
            });
        }

        // Update game state
        game.currentPlayers++;
        game.prizePool += msg.value;

        emit PlayerJoined(gameId, msg.sender, playerIndex);

        // Start the game if it's full
        if (game.currentPlayers == game.maxPlayers) {
            startGame(gameId);
        }
    }

    /**
     * @dev Start a game
     * @param gameId The ID of the game to start
     */
    function startGame(uint256 gameId) internal {
        GameState storage game = games[gameId];

        require(game.gameId == gameId, "Game does not exist");
        require(
            game.status == GameStatus.WAITING,
            "Game is not in waiting state"
        );
        require(game.currentPlayers >= 2, "Need at least 2 players to start");

        game.status = GameStatus.ACTIVE;
        game.startedAt = block.timestamp;

        // Update all players to playing status
        for (uint8 i = 0; i < game.currentPlayers; i++) {
            Player storage player = gamePlayers[gameId][i];
            player.status = PlayerStatus.PLAYING;
        }

        emit GameStarted(gameId);
    }

    /**
     * @dev Roll the dice for the current player
     * @param gameId The ID of the game
     * @return The dice value (1-6)
     */
    function rollDice(uint256 gameId) external returns (uint8) {
        GameState storage game = games[gameId];

        require(game.gameId == gameId, "Game does not exist");
        require(game.status == GameStatus.ACTIVE, "Game is not active");
        require(isPlayerTurn(gameId, msg.sender), "Not your turn");

        // Get the dice value from the Dice contract
        uint8 diceValue = _dice.rollDice();

        emit DiceRolled(gameId, msg.sender, diceValue);

        return diceValue;
    }

    /**
     * @dev Move a piece on the board
     * @param gameId The ID of the game
     * @param pieceIndex The index of the piece to move (0-3)
     * @param diceValue The dice value to use for the move
     */
    function movePiece(
        uint256 gameId,
        uint8 pieceIndex,
        uint8 diceValue
    ) external {
        GameState storage game = games[gameId];

        require(game.gameId == gameId, "Game does not exist");
        require(game.status == GameStatus.ACTIVE, "Game is not active");
        require(isPlayerTurn(gameId, msg.sender), "Not your turn");
        require(pieceIndex < 4, "Invalid piece index");
        require(diceValue >= 1 && diceValue <= 6, "Invalid dice value");

        uint8 playerIndex = getPlayerIndex(gameId, msg.sender);
        PiecePosition storage piece = gamePieces[gameId][playerIndex][
            pieceIndex
        ];

        // If piece is not out, can only move if dice is 6
        if (!piece.isOut) {
            require(diceValue == 6, "Need a 6 to move piece out");
            piece.isOut = true;
            piece.position = getStartPosition(playerIndex);

            emit PieceMoved(gameId, msg.sender, pieceIndex, 0, piece.position);

            // Check for captures
            checkForCaptures(gameId, playerIndex, pieceIndex);

            // If dice is 6, player gets another turn
            if (diceValue == 6) {
                // Keep the same turn
                return;
            }
        } else {
            // Piece is already out, move it
            uint8 oldPosition = piece.position;
            uint8 newPosition = calculateNewPosition(
                playerIndex,
                oldPosition,
                diceValue
            );

            // Check if the move would take the piece home
            if (isHomePosition(playerIndex, newPosition)) {
                piece.isHome = true;
                piece.position = 0; // Reset position

                // Increment pieces home counter
                Player storage player = gamePlayers[gameId][playerIndex];
                player.piecesHome++;

                // Check if player has won
                if (player.piecesHome == 4) {
                    player.status = PlayerStatus.FINISHED;
                    finishGame(gameId, msg.sender);
                    return;
                }
            } else {
                piece.position = newPosition;
            }

            emit PieceMoved(
                gameId,
                msg.sender,
                pieceIndex,
                oldPosition,
                newPosition
            );

            // Check for captures
            checkForCaptures(gameId, playerIndex, pieceIndex);
        }

        // Move to next player's turn
        game.currentTurn = (game.currentTurn + 1) % game.currentPlayers;
    }

    /**
     * @dev Check if a piece can capture other pieces at its current position
     * @param gameId The ID of the game
     * @param playerIndex The index of the player
     * @param pieceIndex The index of the piece
     */
    function checkForCaptures(
        uint256 gameId,
        uint8 playerIndex,
        uint8 pieceIndex
    ) internal {
        PiecePosition storage attackerPiece = gamePieces[gameId][playerIndex][
            pieceIndex
        ];

        // Skip if the piece is home or not out
        if (attackerPiece.isHome || !attackerPiece.isOut) {
            return;
        }

        // Check all other players' pieces
        for (
            uint8 otherPlayerIndex = 0;
            otherPlayerIndex < games[gameId].currentPlayers;
            otherPlayerIndex++
        ) {
            // Skip the current player
            if (otherPlayerIndex == playerIndex) {
                continue;
            }

            // Check all pieces of the other player
            for (
                uint8 otherPieceIndex = 0;
                otherPieceIndex < 4;
                otherPieceIndex++
            ) {
                PiecePosition storage defenderPiece = gamePieces[gameId][
                    otherPlayerIndex
                ][otherPieceIndex];

                // Skip if the piece is home, not out, or on a safe spot
                if (
                    defenderPiece.isHome ||
                    !defenderPiece.isOut ||
                    isSafeSpot(defenderPiece.position)
                ) {
                    continue;
                }

                // Check if the pieces are on the same position
                if (attackerPiece.position == defenderPiece.position) {
                    // Capture the piece
                    defenderPiece.isOut = false;
                    defenderPiece.position = 0;

                    address defenderAddress = gamePlayers[gameId][
                        otherPlayerIndex
                    ].playerAddress;

                    emit PieceCaptured(
                        gameId,
                        msg.sender,
                        defenderAddress,
                        otherPieceIndex
                    );
                }
            }
        }
    }

    /**
     * @dev Finish a game when a player has won
     * @param gameId The ID of the game
     * @param winner The address of the winner
     */
    function finishGame(uint256 gameId, address winner) internal {
        GameState storage game = games[gameId];

        game.status = GameStatus.COMPLETED;
        game.endedAt = block.timestamp;
        game.winner = winner;

        // Transfer the prize pool to the winner
        uint256 prize = game.prizePool;
        game.prizePool = 0;

        emit PlayerFinished(gameId, winner, 1); // Position 1 = winner
        emit GameCompleted(gameId, winner, prize);

        // Transfer the prize to the winner
        (bool success, ) = payable(winner).call{value: prize}("");
        require(success, "Prize transfer failed");
    }

    /**
     * @dev Cancel a game that hasn't started yet
     * @param gameId The ID of the game to cancel
     */
    function cancelGame(uint256 gameId) external {
        GameState storage game = games[gameId];

        require(game.gameId == gameId, "Game does not exist");
        require(
            game.status == GameStatus.WAITING,
            "Game must be in waiting state"
        );
        require(msg.sender == game.creator, "Only creator can cancel the game");

        game.status = GameStatus.CANCELLED;
        game.endedAt = block.timestamp;

        // Refund all players
        for (uint8 i = 0; i < game.currentPlayers; i++) {
            address playerAddress = gamePlayers[gameId][i].playerAddress;
            uint256 refund = game.entryFee;

            // Transfer the refund
            (bool success, ) = payable(playerAddress).call{value: refund}("");
            require(success, "Refund transfer failed");
        }

        // Reset the prize pool
        game.prizePool = 0;

        emit GameCancelled(gameId);
    }

    // Helper functions

    /**
     * @dev Check if it's a player's turn
     * @param gameId The ID of the game
     * @param playerAddress The address of the player
     * @return True if it's the player's turn, false otherwise
     */
    function isPlayerTurn(
        uint256 gameId,
        address playerAddress
    ) public view returns (bool) {
        GameState storage game = games[gameId];
        return
            gamePlayers[gameId][game.currentTurn].playerAddress ==
            playerAddress;
    }

    /**
     * @dev Get the index of a player in a game
     * @param gameId The ID of the game
     * @param playerAddress The address of the player
     * @return The index of the player
     */
    function getPlayerIndex(
        uint256 gameId,
        address playerAddress
    ) public view returns (uint8) {
        GameState storage game = games[gameId];

        for (uint8 i = 0; i < game.currentPlayers; i++) {
            if (gamePlayers[gameId][i].playerAddress == playerAddress) {
                return i;
            }
        }

        revert("Player not found in game");
    }

    /**
     * @dev Get the starting position for a player
     * @param playerIndex The index of the player
     * @return The starting position on the board
     */
    function getStartPosition(uint8 playerIndex) internal pure returns (uint8) {
        // Red: 0, Green: 13, Blue: 26, Yellow: 39
        return playerIndex * 13;
    }

    /**
     * @dev Calculate the new position of a piece after a move
     * @param playerIndex The index of the player
     * @param currentPosition The current position of the piece
     * @param diceValue The dice value
     * @return The new position of the piece
     */
    function calculateNewPosition(
        uint8 playerIndex,
        uint8 currentPosition,
        uint8 diceValue
    ) internal pure returns (uint8) {
        // For simplicity, we're using a linear track of 52 positions (0-51)
        // Each player has a different starting position
        uint8 newPosition = (currentPosition + diceValue) % 52;
        return newPosition;
    }

    /**
     * @dev Check if a position is a safe spot
     * @param position The position to check
     * @return True if the position is a safe spot, false otherwise
     */
    function isSafeSpot(uint8 position) internal pure returns (bool) {
        // Safe spots are at positions 0, 8, 13, 21, 26, 34, 39, 47
        return
            position == 0 ||
            position == 8 ||
            position == 13 ||
            position == 21 ||
            position == 26 ||
            position == 34 ||
            position == 39 ||
            position == 47;
    }

    /**
     * @dev Check if a position is a home position for a player
     * @param playerIndex The index of the player
     * @param position The position to check
     * @return True if the position is a home position, false otherwise
     */
    function isHomePosition(
        uint8 playerIndex,
        uint8 position
    ) internal pure returns (bool) {
        // For simplicity, we'll consider positions 5 steps after the player's
        // starting position + 47 as the home position
        uint8 homePosition = (getStartPosition(playerIndex) + 47) % 52;
        return position == homePosition;
    }

    /**
     * @dev Get game information
     * @param gameId The ID of the game
     * @return Game state information
     */
    function getGameInfo(
        uint256 gameId
    ) external view returns (GameState memory) {
        require(games[gameId].gameId == gameId, "Game does not exist");
        return games[gameId];
    }

    /**
     * @dev Get player information
     * @param gameId The ID of the game
     * @param playerIndex The index of the player
     * @return Player information
     */
    function getPlayerInfo(
        uint256 gameId,
        uint8 playerIndex
    ) external view returns (Player memory) {
        require(games[gameId].gameId == gameId, "Game does not exist");
        require(
            playerIndex < games[gameId].currentPlayers,
            "Player index out of bounds"
        );
        return gamePlayers[gameId][playerIndex];
    }

    /**
     * @dev Get piece information
     * @param gameId The ID of the game
     * @param playerIndex The index of the player
     * @param pieceIndex The index of the piece
     * @return Piece position information
     */
    function getPieceInfo(
        uint256 gameId,
        uint8 playerIndex,
        uint8 pieceIndex
    ) external view returns (PiecePosition memory) {
        require(games[gameId].gameId == gameId, "Game does not exist");
        require(
            playerIndex < games[gameId].currentPlayers,
            "Player index out of bounds"
        );
        require(pieceIndex < 4, "Piece index out of bounds");
        return gamePieces[gameId][playerIndex][pieceIndex];
    }
}
