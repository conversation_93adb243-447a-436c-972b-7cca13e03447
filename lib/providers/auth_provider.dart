import 'dart:math';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:web3dart/web3dart.dart';

// import 'package:flutter/material.dart';
// import 'dart:convert';
// import 'package:ethereum_addresses/ethereum_addresses.dart';
// import 'package:url_launcher/url_launcher.dart';

// State class for authentication
class AuthState {
  final bool isAuthenticated;
  final bool isLoading;
  final String? walletAddress;
  final String? error;
  final double? balance;
  final bool isGuest;

  AuthState({
    this.isAuthenticated = false,
    this.isLoading = false,
    this.walletAddress,
    this.error,
    this.balance,
    this.isGuest = false,
  });

  AuthState copyWith({
    bool? isAuthenticated,
    bool? isLoading,
    String? walletAddress,
    String? error,
    double? balance,
    bool? isGuest,
  }) {
    return AuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isLoading: isLoading ?? this.isLoading,
      walletAddress: walletAddress ?? this.walletAddress,
      error: error ?? this.error,
      balance: balance ?? this.balance,
      isGuest: isGuest ?? this.isGuest,
    );
  }
}

// Auth notifier class
class AuthNotifier extends StateNotifier<AuthState> {
  final Web3Client _web3client;
  final SharedPreferences _prefs;

  // RPC URL for Ethereum (can be changed to any EVM compatible network)
  static const String _rpcUrl = 'https://mainnet.infura.io/v3/your-infura-key';

  // Contract addresses
  static const String _diceContractAddress = '******************************************';
  static const String _gameFactoryContractAddress = '******************************************';

  AuthNotifier(this._web3client, this._prefs) : super(AuthState()) {
    // Check if user was previously authenticated
    _checkPreviousSession();
  }

  // Check if user was previously authenticated
  Future<void> _checkPreviousSession() async {
    final savedAddress = _prefs.getString('wallet_address');
    final isGuest = _prefs.getBool('is_guest') ?? false;

    if (savedAddress != null) {
      state = state.copyWith(isAuthenticated: true, walletAddress: savedAddress, isGuest: false);

      // Fetch balance
      await getBalance();
    } else if (isGuest) {
      state = state.copyWith(isAuthenticated: true, isGuest: true);
    }
  }

  // Connect wallet
  Future<void> connectWallet() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // For now, we'll simulate wallet connection
      // In a real app, you would use a wallet connector like WalletConnect

      // Generate a random Ethereum address for demo purposes
      final random = Random();
      final privateKey = EthPrivateKey.createRandom(random);
      final address = privateKey.address.hex;

      // Save the address to preferences
      await _prefs.setString('wallet_address', address);
      await _prefs.setBool('is_guest', false);

      state = state.copyWith(isAuthenticated: true, walletAddress: address, isLoading: false, isGuest: false);

      // Fetch balance
      await getBalance();
    } catch (e) {
      state = state.copyWith(isLoading: false, error: 'Failed to connect wallet: ${e.toString()}');
    }
  }

  // Sign in as guest
  Future<void> signInAsGuest() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Save guest status to preferences
      await _prefs.setBool('is_guest', true);
      await _prefs.remove('wallet_address');

      state = state.copyWith(isAuthenticated: true, walletAddress: null, isLoading: false, isGuest: true);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: 'Failed to sign in as guest: ${e.toString()}');
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Clear preferences
      await _prefs.remove('wallet_address');
      await _prefs.remove('is_guest');

      state = AuthState();
    } catch (e) {
      state = state.copyWith(isLoading: false, error: 'Failed to sign out: ${e.toString()}');
    }
  }

  // Get wallet balance
  Future<void> getBalance() async {
    if (state.walletAddress == null) return;

    try {
      final address = EthereumAddress.fromHex(state.walletAddress!);
      final balance = await _web3client.getBalance(address);
      final etherBalance = balance.getValueInUnit(EtherUnit.ether);

      state = state.copyWith(balance: etherBalance);
    } catch (e) {
      print('Failed to get balance: ${e.toString()}');
    }
  }

  // Get contract addresses
  String get diceContractAddress => _diceContractAddress;
  String get gameFactoryContractAddress => _gameFactoryContractAddress;
}

// Provider for shared preferences
final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('Should be overridden in main.dart');
});

// Provider for Web3Client
final web3ClientProvider = Provider<Web3Client>((ref) {
  return Web3Client(AuthNotifier._rpcUrl, Client());
});

// Provider for auth state
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final prefs = ref.watch(sharedPreferencesProvider);
  final web3client = ref.watch(web3ClientProvider);
  return AuthNotifier(web3client, prefs);
});
