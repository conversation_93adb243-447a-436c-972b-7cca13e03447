import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ludo_flutter/features/game/logic/game_rules.dart';
import 'package:ludo_flutter/features/game/logic/ludo_board.dart' as board;
import 'package:ludo_flutter/models/player.dart';
import 'package:ludo_flutter/services/audio.dart';
import 'package:web3dart/web3dart.dart';

/// Game state class to hold the current state of the game
class GameState {
  final bool isLoading;
  final bool isRolling;
  final int? diceResult;
  final List<Player> players;
  final int currentPlayerIndex;
  final GameStatus gameStatus;
  final String? error;
  final bool isConnectedToBlockchain;

  GameState({
    this.isLoading = false,
    this.isRolling = false,
    this.diceResult,
    this.players = const [],
    this.currentPlayerIndex = 0,
    this.gameStatus = GameStatus.waiting,
    this.error,
    this.isConnectedToBlockchain = false,
  });

  GameState copyWith({
    bool? isLoading,
    bool? isRolling,
    int? diceResult,
    List<Player>? players,
    int? currentPlayerIndex,
    GameStatus? gameStatus,
    String? error,
    bool? isConnectedToBlockchain,
  }) {
    return GameState(
      isLoading: isLoading ?? this.isLoading,
      isRolling: isRolling ?? this.isRolling,
      diceResult: diceResult ?? this.diceResult,
      players: players ?? this.players,
      currentPlayerIndex: currentPlayerIndex ?? this.currentPlayerIndex,
      gameStatus: gameStatus ?? this.gameStatus,
      error: error,
      isConnectedToBlockchain: isConnectedToBlockchain ?? this.isConnectedToBlockchain,
    );
  }

  Player get currentPlayer => players[currentPlayerIndex];
}

/// Game status enum
enum GameStatus { waiting, active, completed, cancelled }

/// Game notifier class to manage the game state
class GameNotifier extends StateNotifier<GameState> {
  final GameRules _gameRules;
  final AudioService _audioService;

  GameNotifier(this._gameRules, this._audioService) : super(GameState());

  /// Initialize the game with players
  Future<void> initializeGame({
    required List<String> playerNames,
    required List<String> playerAddresses,
    required bool useBlockchain,
    List<bool>? isComputerControlled,
    String? diceContractAddress,
    String? gameFactoryContractAddress,
    Web3Client? web3client,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Create players with tokens
      final players = _createPlayers(
        playerNames,
        playerAddresses,
        isComputerControlled ?? [true, true, false, true], // Default: only player 3 (blue) is human
      );

      // Convert to board.Player objects for game rules
      final boardPlayers = players.map((player) {
        // Convert tokens
        final boardTokens = player.tokens
            .map(
              (token) => board.Token(
                color: board.PlayerColor.values[token.color.index],
                id: token.id,
                state: board.TokenState.values[token.state.index],
                position: token.position,
                steps: token.steps,
                canMove: token.canMove,
              ),
            )
            .toList();

        // Create board.Player
        return board.Player(
          color: board.PlayerColor.values[player.color.index],
          name: player.name,
          address: player.address,
          tokens: boardTokens,
          isActive: player.isActive,
          hasWon: player.hasWon,
        );
      }).toList();

      // Initialize game rules
      _gameRules.initializeGame(boardPlayers);

      state = state.copyWith(
        isLoading: false,
        players: players,
        currentPlayerIndex: 0,
        gameStatus: GameStatus.waiting,
        isConnectedToBlockchain: useBlockchain && web3client != null,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: 'Failed to initialize game: ${e.toString()}');
    }
  }

  /// Create players with tokens
  List<Player> _createPlayers(List<String> playerNames, List<String> playerAddresses, List<bool> isComputerControlled) {
    final playerColors = [PlayerColor.red, PlayerColor.green, PlayerColor.blue, PlayerColor.yellow];
    final players = <Player>[];

    for (int i = 0; i < playerNames.length && i < 4; i++) {
      final tokens = <Token>[];

      // Create 4 tokens for each player
      for (int j = 0; j < 4; j++) {
        tokens.add(Token(color: playerColors[i], id: j));
      }

      players.add(
        Player(
          id: 'player_$i', // Add unique ID for each player
          color: playerColors[i],
          name: playerNames[i],
          address: playerAddresses[i],
          tokens: tokens,
          isComputer: isComputerControlled[i],
        ),
      );
    }

    return players;
  }

  /// Start the game
  void startGame() {
    _gameRules.startGame();
    state = state.copyWith(gameStatus: GameStatus.active);
  }

  /// Roll the dice
  Future<void> rollDice() async {
    if (state.isRolling) return;

    state = state.copyWith(isRolling: true, diceResult: null);

    // Play dice sound
    await _audioService.playDiceSound();

    try {
      // Roll the dice
      final result = await _gameRules.rollDice();

      state = state.copyWith(isRolling: false, diceResult: result);

      // Update which tokens can move
      _updateMovableTokens(result);

      // If no tokens can move, move to next player after a delay
      if (!_canMoveAnyToken()) {
        await Future.delayed(const Duration(seconds: 1));
        _nextPlayer();
      }
    } catch (e) {
      state = state.copyWith(isRolling: false, error: 'Failed to roll dice: ${e.toString()}');
    }
  }

  /// Move a token
  Future<void> moveToken(int tokenId) async {
    if (state.isRolling) return;

    try {
      final success = _gameRules.moveToken(tokenId);

      if (success) {
        // Play move sound
        await _audioService.playMoveSound();

        // Update game state
        final boardPlayer = _gameRules.getCurrentPlayer();
        if (boardPlayer != null) {
          final players = [...state.players];
          // Convert board.PlayerColor to our PlayerColor for comparison
          final boardPlayerColor = boardPlayer.color;
          final index = players.indexWhere((p) => p.color.index == boardPlayerColor.index);

          if (index >= 0) {
            // Update player properties from the board player
            final existingPlayer = players[index];

            // Create a new player with updated properties
            players[index] = Player(
              id: existingPlayer.id,
              color: existingPlayer.color,
              name: existingPlayer.name,
              address: existingPlayer.address,
              isComputer: existingPlayer.isComputer,
              hasWon: boardPlayer.hasWon,
              isActive: boardPlayer.isActive,
              tokens: existingPlayer.tokens.map((token) {
                // Find corresponding token in board player
                final boardToken = boardPlayer.tokens.firstWhere(
                  (t) => t.id == token.id,
                  orElse: () => board.Token(color: board.PlayerColor.values[token.color.index], id: token.id),
                );

                // Update token properties
                return Token(
                  color: token.color,
                  id: token.id,
                  state: TokenState.values[boardToken.state.index],
                  position: boardToken.position,
                  steps: boardToken.steps,
                  canMove: boardToken.canMove,
                );
              }).toList(),
            );
          }

          state = state.copyWith(players: players, currentPlayerIndex: _gameRules.getCurrentPlayer()?.color.index ?? 0);

          // Check if player has won
          if (boardPlayer.hasWon) {
            await _audioService.playWinSound();

            // Check if game is over
            if (players.where((p) => !p.hasWon).length <= 1) {
              state = state.copyWith(gameStatus: GameStatus.completed);
            }
          }
        }
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to move token: ${e.toString()}');
    }
  }

  /// Update which tokens can move with the current dice value
  void _updateMovableTokens(int diceValue) {
    final players = [...state.players];
    final currentPlayer = players[state.currentPlayerIndex];

    currentPlayer.updateMovableTokens(diceValue);

    state = state.copyWith(players: players);
  }

  /// Check if the current player can move any token
  bool _canMoveAnyToken() {
    return state.currentPlayer.canMoveAnyToken(state.diceResult ?? 0);
  }

  /// Move to the next player
  void _nextPlayer() {
    int nextIndex = (state.currentPlayerIndex + 1) % state.players.length;

    // Skip players who have already won
    while (state.players[nextIndex].hasWon) {
      nextIndex = (nextIndex + 1) % state.players.length;
    }

    state = state.copyWith(currentPlayerIndex: nextIndex);
  }
}

/// Provider for the game state
final gameProvider = StateNotifierProvider<GameNotifier, GameState>((ref) {
  final gameRules = GameRules();
  final audioService = AudioService();
  return GameNotifier(gameRules, audioService);
});
