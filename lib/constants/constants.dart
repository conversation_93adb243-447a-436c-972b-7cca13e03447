import 'package:flutter/material.dart';

enum LudoPlayerType { green, yellow, blue, red }

enum LudoGameState { throwDice, pickPawn, moving, finish }

/// Ludo colors constant class
class LudoColor {
  static const Color green = Color(0xFF00AA00); // Green (Computer 2)
  static const Color yellow = Color(0xFFFFCC00); // Yellow (Computer 3)
  static const Color blue = Color(0xFF0088FF); // Blue (You)
  static const Color red = Color(0xFFFF0000); // Red (Computer 1)
}

/// Ludo player names based on the reference image
class LudoPlayerNames {
  static const String computer1 = "Computer 1";
  static const String computer2 = "Computer 2";
  static const String you = "You";
  static const String computer3 = "Computer 3";
}

///Ludo path constant class which contains all the path of the pawns
class LudoPath {
  ///Calculate the path for player to move the pawn with specific step
  static double stepBox(double boardSize, double step) {
    double boxRaw = (boardSize / 15);
    double stepSize = boxRaw * step;
    return stepSize;
  }

  /// Safe path that is the path where the pawns can move without getting killed
  /// These are the star positions on the board based on the reference image
  static const List<List<double>> safeArea = [
    // Star positions on the main track
    [2, 7], // Red path star
    [7, 2], // Green path star
    [12, 7], // Blue path star
    [7, 12], // Yellow path star
    // Additional stars on the board
    [5, 5], // Center area star
    [9, 5], // Center area star
    [5, 9], // Center area star
    [9, 9], // Center area star
  ];

  // Home paths for each player based on the reference image
  // These are the positions of the 4 pieces in each player's home area

  // Green (Computer 2) home path - top right
  static const List<List<double>> greenHomePath = [
    [11.5, 1.5], // Top left piece
    [13.5, 1.5], // Top right piece
    [11.5, 3.5], // Bottom left piece
    [13.5, 3.5], // Bottom right piece
  ];

  // Yellow (Computer 3) home path - bottom right
  static const List<List<double>> yellowHomePath = [
    [11.5, 11.5], // Top left piece
    [13.5, 11.5], // Top right piece
    [11.5, 13.5], // Bottom left piece
    [13.5, 13.5], // Bottom right piece
  ];
  // Blue (You) home path - bottom left
  static const List<List<double>> blueHomePath = [
    [1.5, 11.5], // Top left piece
    [3.5, 11.5], // Top right piece
    [1.5, 13.5], // Bottom left piece
    [3.5, 13.5], // Bottom right piece
  ];

  // Red (Computer 1) home path - top left
  static const List<List<double>> redHomePath = [
    [1.5, 1.5], // Top left piece
    [3.5, 1.5], // Top right piece
    [1.5, 3.5], // Bottom left piece
    [3.5, 3.5], // Bottom right piece
  ];

  // Main paths for each player based on the reference image

  // Green (Computer 2) path - starts from top right
  static const List<List<double>> greenPath = [
    // Starting position
    [7, 1], // Green start
    // Moving right to left along top edge
    [6, 1],
    [5, 1],
    [4, 1],
    [3, 1],
    [2, 1],
    [1, 1],

    // Turn down
    [1, 2],

    // Moving down along left edge
    [1, 3],
    [1, 4],
    [1, 5],
    [1, 6],

    // Star position
    [2, 7], // Star
    // Continue right
    [3, 7],
    [4, 7],
    [5, 7],
    [6, 7],
    [7, 7],

    // Center
    [7, 8],

    // Moving down
    [7, 9],
    [7, 10],
    [7, 11],
    [7, 12],

    // Star position
    [7, 12], // Star
    // Turn right
    [8, 12],

    // Moving right along bottom edge
    [9, 12],
    [10, 12],
    [11, 12],
    [12, 12],
    [13, 12],
    [14, 12],

    // Turn up
    [14, 11],

    // Moving up along right edge
    [14, 10],
    [14, 9],
    [14, 8],
    [14, 7],
    [14, 6],

    // Star position
    [13, 7], // Star
    // Continue left
    [12, 7],
    [11, 7],
    [10, 7],
    [9, 7],
    [8, 7],

    // Home stretch for green
    [8, 6],
    [8, 5],
    [8, 4],
    [8, 3],
    [8, 2],
    [8, 1], // Finish
  ];
  // Yellow (Computer 3) path - starts from bottom right
  static const List<List<double>> yellowPath = [
    // Starting position
    [13, 7], // Yellow start
    // Moving bottom to top along right edge
    [13, 6],
    [13, 5],
    [13, 4],
    [13, 3],
    [13, 2],
    [13, 1],

    // Turn left
    [12, 1],

    // Moving left along top edge
    [11, 1],
    [10, 1],
    [9, 1],
    [8, 1],

    // Star position
    [7, 2], // Star
    // Continue down
    [7, 3],
    [7, 4],
    [7, 5],
    [7, 6],
    [7, 7],

    // Center
    [6, 7],

    // Moving left
    [5, 7],
    [4, 7],
    [3, 7],
    [2, 7],

    // Star position
    [2, 7], // Star
    // Turn down
    [2, 8],

    // Moving down along left edge
    [2, 9],
    [2, 10],
    [2, 11],
    [2, 12],
    [2, 13],
    [2, 14],

    // Turn right
    [3, 14],

    // Moving right along bottom edge
    [4, 14],
    [5, 14],
    [6, 14],
    [7, 14],
    [8, 14],

    // Star position
    [7, 13], // Star
    // Continue up
    [7, 12],
    [7, 11],
    [7, 10],
    [7, 9],
    [7, 8],

    // Home stretch for yellow
    [8, 8],
    [9, 8],
    [10, 8],
    [11, 8],
    [12, 8],
    [13, 8], // Finish
  ];
  // Blue (You) path - starts from bottom left
  static const List<List<double>> bluePath = [
    // Starting position
    [7, 13], // Blue start
    // Moving right to left along bottom edge
    [6, 13],
    [5, 13],
    [4, 13],
    [3, 13],
    [2, 13],
    [1, 13],

    // Turn up
    [1, 12],

    // Moving up along left edge
    [1, 11],
    [1, 10],
    [1, 9],
    [1, 8],

    // Star position
    [2, 7], // Star
    // Continue right
    [3, 7],
    [4, 7],
    [5, 7],
    [6, 7],
    [7, 7],

    // Center
    [7, 6],

    // Moving up
    [7, 5],
    [7, 4],
    [7, 3],
    [7, 2],

    // Star position
    [7, 2], // Star
    // Turn right
    [8, 2],

    // Moving right along top edge
    [9, 2],
    [10, 2],
    [11, 2],
    [12, 2],
    [13, 2],
    [14, 2],

    // Turn down
    [14, 3],

    // Moving down along right edge
    [14, 4],
    [14, 5],
    [14, 6],
    [14, 7],
    [14, 8],

    // Star position
    [13, 7], // Star
    // Continue left
    [12, 7],
    [11, 7],
    [10, 7],
    [9, 7],
    [8, 7],

    // Home stretch for blue
    [7, 8],
    [7, 9],
    [7, 10],
    [7, 11],
    [7, 12],
    [7, 13], // Finish
  ];
  // Red (Computer 1) path - starts from top left
  static const List<List<double>> redPath = [
    // Starting position
    [1, 7], // Red start
    // Moving top to bottom along left edge
    [1, 8],
    [1, 9],
    [1, 10],
    [1, 11],
    [1, 12],
    [1, 13],

    // Turn right
    [2, 13],

    // Moving right along bottom edge
    [3, 13],
    [4, 13],
    [5, 13],
    [6, 13],

    // Star position
    [7, 12], // Star
    // Continue up
    [7, 11],
    [7, 10],
    [7, 9],
    [7, 8],
    [7, 7],

    // Center
    [8, 7],

    // Moving right
    [9, 7],
    [10, 7],
    [11, 7],
    [12, 7],

    // Star position
    [12, 7], // Star
    // Turn up
    [12, 6],

    // Moving up along right edge
    [12, 5],
    [12, 4],
    [12, 3],
    [12, 2],
    [12, 1],
    [12, 0],

    // Turn left
    [11, 0],

    // Moving left along top edge
    [10, 0],
    [9, 0],
    [8, 0],
    [7, 0],
    [6, 0],

    // Star position
    [7, 1], // Star
    // Continue down
    [7, 2],
    [7, 3],
    [7, 4],
    [7, 5],
    [7, 6],

    // Home stretch for red
    [6, 7],
    [5, 7],
    [4, 7],
    [3, 7],
    [2, 7],
    [1, 7], // Finish
  ];
}
