import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ludo_flutter/features/auth/screens/login_screen.dart';
import 'package:ludo_flutter/features/game/screens/game_board_screen.dart';
import 'package:ludo_flutter/features/game/screens/game_lobby_screen.dart';
import 'package:ludo_flutter/providers/auth_provider.dart';
import 'package:ludo_flutter/services/auth_service.dart';
import 'package:ludo_flutter/services/audio.dart';
import 'package:ludo_flutter/utils/theme.dart';
import 'package:shared_preferences/shared_preferences.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);

  // Initialize SharedPreferences
  final sharedPreferences = await SharedPreferences.getInstance();

  // Create ProviderContainer
  final container = ProviderContainer(
    overrides: [
      // Override the sharedPreferencesProvider with the actual instance
      sharedPreferencesProvider.overrideWithValue(sharedPreferences),
    ],
  );

  // Initialize AuthService with the container
  AuthService().initialize(container);

  // Initialize AudioService
  AudioService();

  runApp(UncontrolledProviderScope(container: container, child: const MyApp()));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Ludo Ethereum Game',
      theme: AppTheme.darkTheme(context),
      themeMode: ThemeMode.dark, // Force dark theme
      debugShowCheckedModeBanner: false,
      initialRoute: '/',
      routes: {
        '/': (context) => const LoginScreen(),
        '/game': (context) => const GameLobbyScreen(),
        '/game-board': (context) => const GameBoardScreen(),
      },
    );
  }
}
