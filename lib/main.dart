import 'package:flutter/material.dart';
import 'package:ludo_flutter/provider/ludo_provider.dart';
import 'package:ludo_flutter/views/main_screen.dart';
import 'package:provider/provider.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => LudoProvider(),
      child: MaterialApp(
        title: 'Ludo Flutter',
        theme: ThemeData(primarySwatch: Colors.blue, visualDensity: VisualDensity.adaptivePlatformDensity),
        debugShowCheckedModeBanner: false,
        home: const MainScreen(),
      ),
    );
  }
}
