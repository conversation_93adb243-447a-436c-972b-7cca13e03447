import 'package:flutter/foundation.dart';

/// Enum for feedback types (matches flutter_vibrate package)
enum FeedbackType {
  success,
  error,
  warning,
  selection,
  impact,
  heavy,
  medium,
  light,
}

/// Stub implementation for web platform
class Vibrate {
  static Future<bool> get canVibrate async => false;
  
  static Future<void> feedback(FeedbackType type) async {
    // No-op on web
    debugPrint('Web vibration stub called with type: $type');
  }
  
  static Future<void> vibrateWithPauses(List<Duration> durations) async {
    // No-op on web
  }
}
