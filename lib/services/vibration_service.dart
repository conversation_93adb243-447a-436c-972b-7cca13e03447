import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_vibrate/flutter_vibrate.dart' as flutter_vibrate;

/// Re-export FeedbackType from flutter_vibrate
export 'package:flutter_vibrate/flutter_vibrate.dart' show FeedbackType;

/// Vibration service that works on all platforms
/// Provides a consistent API for vibration that gracefully handles web platform
class VibrationService {
  /// Check if device can vibrate
  static Future<bool> get canVibrate async {
    if (kIsWeb) {
      return false;
    }

    try {
      return await flutter_vibrate.Vibrate.canVibrate;
    } catch (e) {
      debugPrint('Error checking vibration capability: $e');
      return false;
    }
  }

  /// Trigger vibration feedback
  static Future<void> feedback(flutter_vibrate.FeedbackType type) async {
    if (kIsWeb) {
      debugPrint('Web platform - vibration skipped');
      return;
    }

    try {
      flutter_vibrate.Vibrate.feedback(type);
    } catch (e) {
      debugPrint('Error with vibration feedback: $e');
      // Try fallback to system haptics
      _fallbackHaptic(type);
    }
  }

  /// Fallback to system haptics when vibration plugin fails
  static void _fallbackHaptic(flutter_vibrate.FeedbackType type) {
    try {
      switch (type) {
        case flutter_vibrate.FeedbackType.light:
          HapticFeedback.lightImpact();
          break;
        case flutter_vibrate.FeedbackType.medium:
          HapticFeedback.mediumImpact();
          break;
        case flutter_vibrate.FeedbackType.heavy:
          HapticFeedback.heavyImpact();
          break;
        case flutter_vibrate.FeedbackType.selection:
          HapticFeedback.selectionClick();
          break;
        default:
          HapticFeedback.vibrate();
      }
    } catch (e) {
      debugPrint('Fallback haptic feedback failed: $e');
    }
  }
}
