import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ludo_flutter/models/user_model.dart';
import 'package:ludo_flutter/providers/auth_provider.dart';

/// Service for handling authentication with Ethereum wallet
class AuthService {
  // Singleton instance
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;

  // Reference to the ProviderContainer
  late ProviderContainer _container;

  // Initialize with a ProviderContainer
  void initialize(ProviderContainer container) {
    _container = container;
  }

  AuthService._internal();

  // Current user data
  User? _currentUser;

  // Get current user
  User? get currentUser {
    final authState = _container.read(authProvider);

    if (!authState.isAuthenticated) return null;

    if (authState.isGuest) {
      return User(id: 'guest-user', name: 'Guest Player', email: '');
    } else {
      return User(id: authState.walletAddress ?? 'unknown', name: 'Ethereum User', email: '');
    }
  }

  /// Connect with Ethereum wallet
  /// Returns user data if successful
  Future<User?> connectWithEthereumWallet() async {
    try {
      final authNotifier = _container.read(authProvider.notifier);
      await authNotifier.connectWallet();

      final authState = _container.read(authProvider);
      if (authState.isAuthenticated && !authState.isGuest) {
        _currentUser = User(id: authState.walletAddress ?? 'unknown', name: 'Ethereum User', email: '');
        return _currentUser;
      }
      return null;
    } catch (e) {
      // Log error
      return null;
    }
  }

  /// Sign in as guest
  /// Returns a temporary user
  Future<User?> signInAsGuest() async {
    try {
      final authNotifier = _container.read(authProvider.notifier);
      await authNotifier.signInAsGuest();

      _currentUser = User(id: 'guest-user', name: 'Guest Player', email: '');
      return _currentUser;
    } catch (e) {
      // Log error
      return null;
    }
  }

  /// Sign out current user
  Future<void> signOut() async {
    final authNotifier = _container.read(authProvider.notifier);
    await authNotifier.signOut();
    _currentUser = null;
  }

  /// Check if user is authenticated
  bool get isAuthenticated {
    final authState = _container.read(authProvider);
    return authState.isAuthenticated;
  }

  /// Check if user is a guest
  bool get isGuest {
    final authState = _container.read(authProvider);
    return authState.isGuest;
  }

  /// Get wallet address
  String? get walletAddress {
    final authState = _container.read(authProvider);
    return authState.walletAddress;
  }

  /// Get wallet balance
  double? get walletBalance {
    final authState = _container.read(authProvider);
    return authState.balance;
  }
}
