import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';

// Import vibration service that handles web platform gracefully
import 'vibration_service.dart';

/// Audio service for game sound effects and haptic feedback
class AudioService {
  // Singleton instance
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;

  // Audio player
  late AudioPlayer _audioPlayer;
  late AudioPlayer _backgroundMusicPlayer;

  // Sound enabled flag
  bool _soundEnabled = true;

  // Haptic feedback enabled flag
  bool _hapticEnabled = true;

  // Background music enabled flag
  bool _musicEnabled = true;

  // Vibration capability flag
  bool _canVibrate = false;

  // Getters for status
  bool get isSoundEnabled => _soundEnabled;
  bool get isHapticEnabled => _hapticEnabled && _canVibrate;
  bool get isMusicEnabled => _musicEnabled;

  // Initialize the audio service
  AudioService._internal() {
    _audioPlayer = AudioPlayer();
    _backgroundMusicPlayer = AudioPlayer();
    _initVibration();
    _initBackgroundMusic();
  }

  // Initialize vibration capabilities
  Future<void> _initVibration() async {
    try {
      // Check vibration capability using our platform-safe service
      _canVibrate = await VibrationService.canVibrate;
      if (!_canVibrate) {
        debugPrint('Device does not support vibration or is running on web');
      }
    } catch (e) {
      // Handle any errors gracefully
      _canVibrate = false;
      debugPrint('Vibration initialization error: $e');
    }
  }

  // Safe vibration method using our platform-safe service
  void _safeVibrate(FeedbackType type) {
    if (!_hapticEnabled || !_canVibrate) return;

    // Use our platform-safe vibration service
    VibrationService.feedback(type);
  }

  // Initialize background music
  Future<void> _initBackgroundMusic() async {
    try {
      // Skip on web platform for now to avoid issues
      if (kIsWeb) {
        debugPrint('Running on web platform, background music disabled');
        return;
      }

      await _backgroundMusicPlayer.setAsset('assets/sounds/roll_the_dice.mp3');
      await _backgroundMusicPlayer.setLoopMode(LoopMode.all);
      if (_musicEnabled) {
        await _backgroundMusicPlayer.play();
        await _backgroundMusicPlayer.setVolume(0.3); // Lower volume for background music
      }
    } catch (e) {
      // Log error but don't crash
      debugPrint('Background music initialization error: $e');
    }
  }

  /// Toggle sound effects on/off
  void toggleSound() {
    _soundEnabled = !_soundEnabled;
    if (!_soundEnabled) {
      _audioPlayer.stop();
    }
  }

  /// Toggle haptic feedback on/off
  void toggleHaptic() {
    _hapticEnabled = !_hapticEnabled;
  }

  /// Toggle background music on/off
  Future<void> toggleMusic() async {
    _musicEnabled = !_musicEnabled;

    // Skip on web platform
    if (kIsWeb) {
      debugPrint('Running on web platform, background music toggle skipped');
      return;
    }

    try {
      if (_musicEnabled) {
        await _backgroundMusicPlayer.play();
      } else {
        await _backgroundMusicPlayer.pause();
      }
    } catch (e) {
      debugPrint('Error toggling background music: $e');
    }
  }

  /// Safely play a sound asset with error handling
  Future<void> _safePlaySound(String assetPath) async {
    if (!_soundEnabled) return;

    // Skip on web platform for now to avoid issues
    if (kIsWeb) {
      debugPrint('Running on web platform, sound disabled: $assetPath');
      return;
    }

    try {
      await _audioPlayer.stop();
      var duration = await _audioPlayer.setAsset(assetPath);
      await _audioPlayer.play();
      await Future.delayed(duration ?? Duration.zero);
    } catch (e) {
      // Log error but don't crash
      debugPrint('Error playing sound $assetPath: $e');
    }
  }

  /// Play piece movement sound with haptic feedback
  Future<void> playMoveSound() async {
    try {
      // Play sound if enabled
      await _safePlaySound('assets/sounds/move.wav');

      // Provide haptic feedback
      _safeVibrate(FeedbackType.light);
    } catch (e) {
      // Log error but don't crash
      debugPrint('Error in playMoveSound: $e');
    }
  }

  /// Play capture/kill sound with haptic feedback
  Future<void> playCaptureSound() async {
    try {
      // Play sound if enabled
      await _safePlaySound('assets/sounds/capture.mp3');

      // Provide haptic feedback
      _safeVibrate(FeedbackType.medium);
    } catch (e) {
      // Log error but don't crash
      debugPrint('Error in playCaptureSound: $e');
    }
  }

  /// Play dice roll sound with haptic feedback
  Future<void> playDiceSound() async {
    try {
      // Play sound if enabled
      await _safePlaySound('assets/sounds/dice.mp3');

      // Provide haptic feedback
      _safeVibrate(FeedbackType.selection);
    } catch (e) {
      // Log error but don't crash
      debugPrint('Error in playDiceSound: $e');
    }
  }

  /// Play win sound with haptic feedback
  Future<void> playWinSound() async {
    try {
      // Play sound if enabled
      await _safePlaySound('assets/sounds/win.mp3');

      // Multiple vibrations for win
      _safeVibrate(FeedbackType.success);
      await Future.delayed(const Duration(milliseconds: 300));
      _safeVibrate(FeedbackType.success);
    } catch (e) {
      // Log error but don't crash
      debugPrint('Error in playWinSound: $e');
    }
  }

  /// Play laugh sound (when capturing opponent's piece)
  Future<void> playLaughSound() async {
    try {
      // Play sound if enabled
      await _safePlaySound('assets/sounds/laugh.mp3');

      // Provide haptic feedback
      _safeVibrate(FeedbackType.heavy);
    } catch (e) {
      // Log error but don't crash
      debugPrint('Error in playLaughSound: $e');
    }
  }

  /// Dispose audio resources
  void dispose() {
    _audioPlayer.dispose();
    _backgroundMusicPlayer.dispose();
  }
}
