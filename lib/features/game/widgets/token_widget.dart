import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ludo_flutter/features/game/logic/ludo_board.dart';
import 'package:ludo_flutter/providers/game_provider.dart';
import 'package:ludo_flutter/services/audio.dart';

/// A widget that represents a player's token on the board
class TokenWidget extends ConsumerWidget {
  /// The token data
  final Token token;

  /// The token's index in the player's tokens list
  final int tokenIndex;

  /// The token's size
  final double size;

  /// Whether the token is highlighted (can be moved)
  final bool isHighlighted;

  /// Callback when the token is tapped
  final Function(Token token)? onTap;

  const TokenWidget({
    super.key,
    required this.token,
    required this.tokenIndex,
    this.size = 30,
    this.isHighlighted = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the color based on the token's player color
    final Color tokenColor = _getColorForToken(token.color);

    // Build the token widget
    return GestureDetector(
          onTap: isHighlighted
              ? () {
                  // Play sound
                  AudioService().playMoveSound();

                  // Call the onTap callback if provided
                  if (onTap != null) {
                    onTap!(token);
                  }

                  // Move the token using the game provider
                  ref.read(gameProvider.notifier).moveToken(tokenIndex);
                }
              : null,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: tokenColor,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 2),
              boxShadow: isHighlighted ? [BoxShadow(color: tokenColor.withAlpha(128), blurRadius: 10, spreadRadius: 2)] : null,
            ),
            child: Center(
              child: Text(
                (tokenIndex + 1).toString(),
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: size * 0.4),
              ),
            ),
          ),
        )
        .animate(target: isHighlighted ? 1 : 0)
        .shimmer(
          duration: const Duration(milliseconds: 1500),
          color: Colors.white.withAlpha(128),
          size: 0.5,
          delay: const Duration(milliseconds: 200),
        )
        .scale(
          begin: const Offset(1, 1),
          end: const Offset(1.1, 1.1),
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
          alignment: Alignment.center,
        );
  }

  /// Get the color for a token based on its player color
  Color _getColorForToken(PlayerColor color) {
    switch (color) {
      case PlayerColor.red:
        return Colors.red.shade600;
      case PlayerColor.green:
        return Colors.green.shade600;
      case PlayerColor.blue:
        return Colors.blue.shade600;
      case PlayerColor.yellow:
        return Colors.amber.shade600;
    }
  }
}

/// A widget that shows a token at a specific position on the board
class PositionedTokenWidget extends ConsumerWidget {
  /// The token data
  final Token token;

  /// The token's index in the player's tokens list
  final int tokenIndex;

  /// The token's size
  final double size;

  /// The board size
  final double boardSize;

  /// Whether the token is highlighted (can be moved)
  final bool isHighlighted;

  /// Callback when the token is tapped
  final Function(Token token)? onTap;

  const PositionedTokenWidget({
    super.key,
    required this.token,
    required this.tokenIndex,
    required this.boardSize,
    this.size = 30,
    this.isHighlighted = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Calculate the position of the token on the board
    final Offset position = _calculateTokenPosition(token, boardSize);

    // Build the positioned token widget
    return Positioned(
      left: position.dx - (size / 2),
      top: position.dy - (size / 2),
      child: TokenWidget(token: token, tokenIndex: tokenIndex, size: size, isHighlighted: isHighlighted, onTap: onTap),
    );
  }

  /// Calculate the position of a token on the board
  Offset _calculateTokenPosition(Token token, double boardSize) {
    // If the token is at home, position it in the home area
    if (token.state == TokenState.home) {
      return _getHomePosition(token.color, tokenIndex, boardSize);
    }

    // If the token is finished, position it in the finish area
    if (token.state == TokenState.finished) {
      return _getFinishPosition(token.color, tokenIndex, boardSize);
    }

    // Otherwise, position it on the track
    return _getTrackPosition(token.position, boardSize);
  }

  /// Get the position of a token in the home area
  Offset _getHomePosition(PlayerColor color, int index, double boardSize) {
    final double cellSize = boardSize / 15;
    final double homeInset = cellSize;
    final double homeSize = cellSize * 6 - (2 * homeInset);

    // Calculate the position based on the token index and player color
    switch (color) {
      case PlayerColor.red:
        return index == 0
            ? Offset(homeInset + homeSize * 0.25, homeInset + homeSize * 0.25)
            : index == 1
            ? Offset(homeInset + homeSize * 0.75, homeInset + homeSize * 0.25)
            : index == 2
            ? Offset(homeInset + homeSize * 0.25, homeInset + homeSize * 0.75)
            : Offset(homeInset + homeSize * 0.75, homeInset + homeSize * 0.75);
      case PlayerColor.green:
        return index == 0
            ? Offset(boardSize - homeInset - homeSize * 0.25, homeInset + homeSize * 0.25)
            : index == 1
            ? Offset(boardSize - homeInset - homeSize * 0.75, homeInset + homeSize * 0.25)
            : index == 2
            ? Offset(boardSize - homeInset - homeSize * 0.25, homeInset + homeSize * 0.75)
            : Offset(boardSize - homeInset - homeSize * 0.75, homeInset + homeSize * 0.75);
      case PlayerColor.blue:
        return index == 0
            ? Offset(boardSize - homeInset - homeSize * 0.25, boardSize - homeInset - homeSize * 0.25)
            : index == 1
            ? Offset(boardSize - homeInset - homeSize * 0.75, boardSize - homeInset - homeSize * 0.25)
            : index == 2
            ? Offset(boardSize - homeInset - homeSize * 0.25, boardSize - homeInset - homeSize * 0.75)
            : Offset(boardSize - homeInset - homeSize * 0.75, boardSize - homeInset - homeSize * 0.75);
      case PlayerColor.yellow:
        return index == 0
            ? Offset(homeInset + homeSize * 0.25, boardSize - homeInset - homeSize * 0.25)
            : index == 1
            ? Offset(homeInset + homeSize * 0.75, boardSize - homeInset - homeSize * 0.25)
            : index == 2
            ? Offset(homeInset + homeSize * 0.25, boardSize - homeInset - homeSize * 0.75)
            : Offset(homeInset + homeSize * 0.75, boardSize - homeInset - homeSize * 0.75);
    }
  }

  /// Get the position of a token in the finish area
  Offset _getFinishPosition(PlayerColor color, int index, double boardSize) {
    final double cellSize = boardSize / 15;
    final double centerSize = 3 * cellSize;
    final double centerOffset = boardSize / 2 - centerSize / 2;

    // Calculate the position based on the player color
    switch (color) {
      case PlayerColor.red:
        return Offset(boardSize / 2, centerOffset + (index * cellSize / 2));
      case PlayerColor.green:
        return Offset(boardSize - centerOffset - (index * cellSize / 2), boardSize / 2);
      case PlayerColor.blue:
        return Offset(boardSize / 2, boardSize - centerOffset - (index * cellSize / 2));
      case PlayerColor.yellow:
        return Offset(centerOffset + (index * cellSize / 2), boardSize / 2);
    }
  }

  /// Get the position of a token on the track
  Offset _getTrackPosition(int position, double boardSize) {
    // Define the track positions
    final List<Offset> trackPositions = _generateTrackPositions(boardSize);

    // Return the position on the track
    if (position >= 0 && position < trackPositions.length) {
      return trackPositions[position];
    }

    // Default position (center of the board)
    return Offset(boardSize / 2, boardSize / 2);
  }

  /// Generate the track positions for the Ludo board
  List<Offset> _generateTrackPositions(double boardSize) {
    final double cellSize = boardSize / 15;
    final List<Offset> positions = [];

    // Define the main track positions (52 positions in total)
    // Starting from the red start position and moving clockwise

    // Red start (bottom of red home path) to green start
    for (int i = 0; i < 13; i++) {
      if (i == 0) {
        // Red start position
        positions.add(Offset(7 * cellSize, 6 * cellSize));
      } else {
        // Moving right along the top track
        positions.add(Offset((7 + i) * cellSize, 6 * cellSize));
      }
    }

    // Green start (left of green home path) to blue start
    for (int i = 0; i < 13; i++) {
      if (i == 0) {
        // Green start position
        positions.add(Offset(8 * cellSize, 7 * cellSize));
      } else {
        // Moving down along the right track
        positions.add(Offset(8 * cellSize, (7 + i) * cellSize));
      }
    }

    // Blue start (top of blue home path) to yellow start
    for (int i = 0; i < 13; i++) {
      if (i == 0) {
        // Blue start position
        positions.add(Offset(7 * cellSize, 8 * cellSize));
      } else {
        // Moving left along the bottom track
        positions.add(Offset((7 - i) * cellSize, 8 * cellSize));
      }
    }

    // Yellow start (right of yellow home path) to red start
    for (int i = 0; i < 13; i++) {
      if (i == 0) {
        // Yellow start position
        positions.add(Offset(6 * cellSize, 7 * cellSize));
      } else {
        // Moving up along the left track
        positions.add(Offset(6 * cellSize, (7 - i) * cellSize));
      }
    }

    return positions;
  }
}
