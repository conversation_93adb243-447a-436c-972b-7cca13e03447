import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ludo_flutter/constants/constants.dart';
import 'package:ludo_flutter/features/game/widgets/positioned_token_widget.dart';
import 'package:ludo_flutter/models/player.dart';
import 'package:ludo_flutter/providers/game_provider.dart';
import 'package:ludo_flutter/services/audio.dart';
import 'package:ludo_flutter/utils/theme.dart';

class GameBoardScreen extends ConsumerStatefulWidget {
  const GameBoardScreen({super.key});

  @override
  ConsumerState<GameBoardScreen> createState() => _GameBoardScreenState();
}

class _GameBoardScreenState extends ConsumerState<GameBoardScreen> {
  // Audio service for sound effects and haptic feedback
  final AudioService _audioService = AudioService();

  // Player colors (matching the Ludo board colors from the reference image)
  final List<Color> _playerColors = [
    LudoColor.red, // Red - Computer 1
    LudoColor.green, // Green - Computer 2
    LudoColor.blue, // Blue - You
    LudoColor.yellow, // Yellow - Computer 3
  ];

  // Player names based on the reference image
  final List<String> _playerNames = [
    LudoPlayerNames.computer1, // Red - Computer 1
    LudoPlayerNames.computer2, // Green - Computer 2
    LudoPlayerNames.you, // Blue - You
    LudoPlayerNames.computer3, // Yellow - Computer 3
  ];

  @override
  void initState() {
    super.initState();

    // Use post-frame callback to initialize the game after the first build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeGame();
    });
  }

  // Initialize the game
  Future<void> _initializeGame() async {
    // Get the game provider
    final gameNotifier = ref.read(gameProvider.notifier);

    // Create player addresses (in a real app, these would come from wallet connections)
    final playerAddresses = [
      '******************************************', // Computer 1
      '******************************************', // Computer 2
      '******************************************', // You (player)
      '******************************************', // Computer 3
    ];

    // Computer-controlled players are defined in the initializeGame call below

    // Initialize the game with player names from the reference image
    await gameNotifier.initializeGame(
      playerNames: _playerNames,
      playerAddresses: playerAddresses,
      useBlockchain: false, // Set to true when blockchain is ready
      isComputerControlled: [
        true, // Computer 1 (Red)
        true, // Computer 2 (Green)
        false, // You (Blue)
        true, // Computer 3 (Yellow)
      ],
    );

    // Start the game
    gameNotifier.startGame();
  }

  @override
  Widget build(BuildContext context) {
    // Listen for game status changes
    ref.listen(gameProvider, (previous, current) {
      // Check if the game status has changed to completed
      if (previous?.gameStatus != GameStatus.completed && current.gameStatus == GameStatus.completed) {
        // Find the winner
        final winner = current.players.firstWhere((player) => player.hasWon, orElse: () => current.players.first);

        // Show win dialog
        _showWinDialog(winner);
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Ludo Game'),
        backgroundColor: AppTheme.cardColor,
        actions: [
          IconButton(icon: const Icon(Icons.info_outline), onPressed: _showGameInfo),
          IconButton(icon: const Icon(Icons.more_vert), onPressed: _showGameMenu),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Player turn indicator
            _buildPlayerTurnIndicator(),

            // Game board
            Expanded(child: _buildGameBoard()),

            // Dice and controls
            _buildDiceAndControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayerTurnIndicator() {
    // Get the game state from the provider
    final gameState = ref.watch(gameProvider);

    // Get the current player
    final currentPlayerIndex = gameState.currentPlayerIndex;
    final currentPlayer = gameState.players.isNotEmpty ? gameState.players[currentPlayerIndex] : null;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      color: AppTheme.cardColor,
      child: Row(
        children: [
          if (currentPlayer != null) ...[
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(color: _playerColors[currentPlayer.color.index], shape: BoxShape.circle),
            ),
            const SizedBox(width: 8),
            Text('${currentPlayer.name}\'s Turn', style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          ] else ...[
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(color: Colors.grey, shape: BoxShape.circle),
            ),
            const SizedBox(width: 8),
            const Text('Waiting for players...', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          ],
          const Spacer(),
          if (gameState.diceResult != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(color: Colors.white.withAlpha(26), borderRadius: BorderRadius.circular(16)),
              child: Row(
                children: [
                  const Icon(Icons.casino, size: 18),
                  const SizedBox(width: 4),
                  Text('Rolled: ${gameState.diceResult}', style: const TextStyle(fontWeight: FontWeight.bold)),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildGameBoard() {
    // Get the game state from the provider
    final gameState = ref.watch(gameProvider);

    return Center(
      child: AspectRatio(
        aspectRatio: 1.0,
        child: Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [BoxShadow(color: Colors.black.withAlpha(51), blurRadius: 10, offset: const Offset(0, 4))],
          ),
          child: LayoutBuilder(
            builder: (context, constraints) {
              final double boardSize = min(constraints.maxWidth, constraints.maxHeight);

              return Stack(
                children: [
                  // Draw the board
                  CustomPaint(painter: LudoBoardPainter(), size: Size.infinite),

                  // Draw the tokens
                  if (gameState.players.isNotEmpty) ..._buildTokens(gameState, boardSize),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  /// Build the tokens for all players
  List<Widget> _buildTokens(GameState gameState, double boardSize) {
    final List<Widget> tokens = [];
    final double tokenSize = boardSize / 20; // Token size relative to board

    // Add tokens for each player
    for (int playerIndex = 0; playerIndex < gameState.players.length; playerIndex++) {
      final player = gameState.players[playerIndex];
      final isCurrentPlayer = playerIndex == gameState.currentPlayerIndex;

      // Add tokens for this player
      for (int tokenIndex = 0; tokenIndex < player.tokens.length; tokenIndex++) {
        final token = player.tokens[tokenIndex];
        final isHighlighted = isCurrentPlayer && token.canMove;

        // Calculate the position of the token on the board
        final Offset position = _calculateTokenPosition(token, boardSize);

        tokens.add(
          Positioned(
            left: position.dx - (tokenSize / 2),
            top: position.dy - (tokenSize / 2),
            child: TokenWidget(
              token: token,
              tokenIndex: tokenIndex,
              size: tokenSize,
              isHighlighted: isHighlighted,
              onTap: (token) {
                // Handle token tap
                _audioService.playMoveSound();
              },
            ),
          ),
        );
      }
    }

    return tokens;
  }

  Widget _buildDiceAndControls() {
    // Get the game state from the provider
    final gameState = ref.watch(gameProvider);

    // Get the current player
    final currentPlayerIndex = gameState.currentPlayerIndex;
    final currentPlayer = gameState.players.isNotEmpty ? gameState.players[currentPlayerIndex] : null;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        border: Border(top: BorderSide(color: Colors.white.withAlpha(26))),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Player info
          if (currentPlayer != null)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  currentPlayer.name,
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: _playerColors[currentPlayer.color.index]),
                ),
                const SizedBox(height: 4),
                Text(
                  'Pieces Home: ${currentPlayer.tokensAtHome}/4',
                  style: TextStyle(fontSize: 12, color: Colors.white.withAlpha(179)),
                ),
                const SizedBox(height: 4),
                Text(
                  'Pieces Finished: ${currentPlayer.tokensFinished}/4',
                  style: TextStyle(fontSize: 12, color: Colors.white.withAlpha(179)),
                ),
              ],
            ),

          // Dice
          GestureDetector(
            onTap: gameState.isRolling ? null : () => _rollDice(),
            child: Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: gameState.isRolling ? Colors.white.withAlpha(26) : AppTheme.primaryColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow: gameState.isRolling
                    ? null
                    : [BoxShadow(color: AppTheme.primaryColor.withAlpha(102), blurRadius: 8, offset: const Offset(0, 4))],
              ),
              child: gameState.isRolling
                  ? const Center(
                      child: SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2),
                      ),
                    )
                  : Center(
                      child: gameState.diceResult == null
                          ? const Icon(Icons.casino, color: Colors.white, size: 32)
                          : Text(
                              gameState.diceResult.toString(),
                              style: const TextStyle(color: Colors.white, fontSize: 32, fontWeight: FontWeight.bold),
                            ),
                    ),
            ).animate(target: gameState.isRolling ? 1 : 0).shake(hz: 5, rotation: 0.15),
          ),

          // Menu/options
          OutlinedButton.icon(
            icon: const Icon(Icons.menu),
            label: const Text('Menu'),
            onPressed: _showGameMenu,
            style: OutlinedButton.styleFrom(side: BorderSide(color: Colors.white.withAlpha(77))),
          ),
        ],
      ),
    );
  }

  Future<void> _rollDice() async {
    // Get the game provider
    final gameNotifier = ref.read(gameProvider.notifier);

    // Roll the dice
    await gameNotifier.rollDice();

    // The game provider will handle all the logic for:
    // - Playing sounds
    // - Updating the UI
    // - Handling the game rules
    // - Moving to the next player
  }

  void _showGameInfo() {
    // Play selection sound with haptic feedback
    _audioService.playDiceSound();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Game Rules'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('• Roll a 6 to move a piece out of home.'),
              SizedBox(height: 8),
              Text('• Land on an opponent\'s piece to send it back home.'),
              SizedBox(height: 8),
              Text('• Rolling a 6 gives you an extra turn.'),
              SizedBox(height: 8),
              Text('• Get all four pieces to home triangle to win.'),
              SizedBox(height: 8),
              Text('• Play with Ethereum smart contracts for transparent gameplay.'),
            ],
          ),
        ),
        actions: [TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Close'))],
      ),
    );
  }

  void _showGameMenu() {
    // Play selection sound with haptic feedback
    _audioService.playMoveSound();

    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Sound settings
              SwitchListTile(
                secondary: const Icon(Icons.volume_up),
                title: const Text('Sound Effects'),
                value: _audioService.isSoundEnabled,
                onChanged: (value) {
                  setState(() {
                    _audioService.toggleSound();
                  });
                },
              ),

              // Haptic feedback settings
              SwitchListTile(
                secondary: const Icon(Icons.vibration),
                title: const Text('Haptic Feedback'),
                value: _audioService.isHapticEnabled,
                onChanged: (value) {
                  setState(() {
                    _audioService.toggleHaptic();
                  });
                },
              ),

              // Background music settings
              SwitchListTile(
                secondary: const Icon(Icons.music_note),
                title: const Text('Background Music'),
                value: _audioService.isMusicEnabled,
                onChanged: (value) async {
                  await _audioService.toggleMusic();
                  setState(() {});
                },
              ),

              const Divider(),

              // Game actions
              ListTile(
                leading: const Icon(Icons.refresh),
                title: const Text('Restart Game'),
                onTap: () {
                  Navigator.pop(context);
                  _restartGame();
                },
              ),
              ListTile(
                leading: const Icon(Icons.exit_to_app),
                title: const Text('Exit Game'),
                onTap: () {
                  _audioService.playMoveSound();
                  Navigator.pop(context);
                  Navigator.pop(context); // Return to game lobby
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Restart the game
  void _restartGame() {
    // Play sound
    _audioService.playDiceSound();

    // Re-initialize the game
    _initializeGame();
  }

  /// Calculate the position of a token on the board
  Offset _calculateTokenPosition(Token token, double boardSize) {
    final double cellSize = boardSize / 15;

    // If the token is at home, position it in the home area
    if (token.state == TokenState.home) {
      // Calculate home position based on player color and token index
      switch (token.color) {
        case PlayerColor.red:
          // Red home positions (top-left)
          final List<Offset> homePositions = [
            Offset(1.5 * cellSize, 1.5 * cellSize),
            Offset(3.5 * cellSize, 1.5 * cellSize),
            Offset(1.5 * cellSize, 3.5 * cellSize),
            Offset(3.5 * cellSize, 3.5 * cellSize),
          ];
          return homePositions[token.id];
        case PlayerColor.green:
          // Green home positions (top-right)
          final List<Offset> homePositions = [
            Offset(boardSize - 3.5 * cellSize, 1.5 * cellSize),
            Offset(boardSize - 1.5 * cellSize, 1.5 * cellSize),
            Offset(boardSize - 3.5 * cellSize, 3.5 * cellSize),
            Offset(boardSize - 1.5 * cellSize, 3.5 * cellSize),
          ];
          return homePositions[token.id];
        case PlayerColor.blue:
          // Blue home positions (bottom-left)
          final List<Offset> homePositions = [
            Offset(1.5 * cellSize, boardSize - 3.5 * cellSize),
            Offset(3.5 * cellSize, boardSize - 3.5 * cellSize),
            Offset(1.5 * cellSize, boardSize - 1.5 * cellSize),
            Offset(3.5 * cellSize, boardSize - 1.5 * cellSize),
          ];
          return homePositions[token.id];
        case PlayerColor.yellow:
          // Yellow home positions (bottom-right)
          final List<Offset> homePositions = [
            Offset(boardSize - 3.5 * cellSize, boardSize - 3.5 * cellSize),
            Offset(boardSize - 1.5 * cellSize, boardSize - 3.5 * cellSize),
            Offset(boardSize - 3.5 * cellSize, boardSize - 1.5 * cellSize),
            Offset(boardSize - 1.5 * cellSize, boardSize - 1.5 * cellSize),
          ];
          return homePositions[token.id];
      }
    }

    // If the token is on the board, calculate its position based on the path
    // For simplicity, we'll use a fixed position for now
    return Offset(7.5 * cellSize, 7.5 * cellSize);
  }

  // Show win dialog when a player wins
  void _showWinDialog(Player winner) {
    // Play win sound
    _audioService.playWinSound();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(
          '${winner.name} Wins!',
          style: TextStyle(color: _playerColors[winner.color.index], fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Trophy icon
            Icon(
              Icons.emoji_events,
              color: _playerColors[winner.color.index],
              size: 64,
            ).animate().scale(duration: const Duration(milliseconds: 500), curve: Curves.elasticOut),

            const SizedBox(height: 16),

            // Win message
            Text('Congratulations! ${winner.name} has won the game!', textAlign: TextAlign.center),

            const SizedBox(height: 8),

            // Blockchain message
            if (!winner.address.startsWith('guest'))
              Text(
                'A reward of 0.01 ETH has been sent to your wallet.',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.green.shade600, fontWeight: FontWeight.bold),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _restartGame();
            },
            child: const Text('Play Again'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Return to lobby
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.primaryColor),
            child: const Text('Back to Lobby'),
          ),
        ],
      ),
    );
  }
}

// Custom painter for the Ludo board
class LudoBoardPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final double boardSize = size.width;
    final double cellSize = boardSize / 15;

    // Define colors based on the reference image
    final Paint redPaint = Paint()..color = LudoColor.red;
    final Paint greenPaint = Paint()..color = LudoColor.green;
    final Paint bluePaint = Paint()..color = LudoColor.blue;
    final Paint yellowPaint = Paint()..color = LudoColor.yellow;
    final Paint whitePaint = Paint()..color = Colors.white;
    final Paint lightGreyPaint = Paint()..color = Colors.grey.shade200;
    final Paint blackPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    final Paint safePaint = Paint()..color = Colors.grey.shade300;
    final Paint starPaint = Paint()..color = Colors.grey.shade400;
    final Paint backgroundPaint = Paint()..color = Colors.blue.shade800;

    // Draw the background (blue pattern in reference image)
    canvas.drawRect(Rect.fromLTWH(0, 0, boardSize, boardSize), backgroundPaint);

    // Draw home bases (corners) based on the reference image
    final double baseSize = cellSize * 6;

    // Red base (top-left) - Computer 1
    canvas.drawRect(Rect.fromLTWH(0, 0, baseSize, baseSize), redPaint);
    // Green base (top-right) - Computer 2
    canvas.drawRect(Rect.fromLTWH(boardSize - baseSize, 0, baseSize, baseSize), greenPaint);
    // Blue base (bottom-left) - You
    canvas.drawRect(Rect.fromLTWH(0, boardSize - baseSize, baseSize, baseSize), bluePaint);
    // Yellow base (bottom-right) - Computer 3
    canvas.drawRect(Rect.fromLTWH(boardSize - baseSize, boardSize - baseSize, baseSize, baseSize), yellowPaint);

    // Draw white home squares inside each base
    final double homeInset = cellSize;
    final double homeSquareSize = baseSize - 2 * homeInset;

    // Red home square
    canvas.drawRect(Rect.fromLTWH(homeInset, homeInset, homeSquareSize, homeSquareSize), whitePaint);
    canvas.drawRect(Rect.fromLTWH(homeInset, homeInset, homeSquareSize, homeSquareSize), blackPaint);

    // Green home square
    canvas.drawRect(Rect.fromLTWH(boardSize - homeInset - homeSquareSize, homeInset, homeSquareSize, homeSquareSize), whitePaint);
    canvas.drawRect(Rect.fromLTWH(boardSize - homeInset - homeSquareSize, homeInset, homeSquareSize, homeSquareSize), blackPaint);

    // Yellow home square
    canvas.drawRect(Rect.fromLTWH(homeInset, boardSize - homeInset - homeSquareSize, homeSquareSize, homeSquareSize), whitePaint);
    canvas.drawRect(Rect.fromLTWH(homeInset, boardSize - homeInset - homeSquareSize, homeSquareSize, homeSquareSize), blackPaint);

    // Blue home square
    canvas.drawRect(
      Rect.fromLTWH(
        boardSize - homeInset - homeSquareSize,
        boardSize - homeInset - homeSquareSize,
        homeSquareSize,
        homeSquareSize,
      ),
      whitePaint,
    );
    canvas.drawRect(
      Rect.fromLTWH(
        boardSize - homeInset - homeSquareSize,
        boardSize - homeInset - homeSquareSize,
        homeSquareSize,
        homeSquareSize,
      ),
      blackPaint,
    );

    // Draw the main track (outer square)
    _drawMainTrack(canvas, boardSize, cellSize, whitePaint, blackPaint, safePaint, starPaint);

    // Draw center home area
    final double centerSize = 3 * cellSize;
    final double centerOffset = boardSize / 2 - centerSize / 2;

    // Draw center square
    canvas.drawRect(Rect.fromLTWH(centerOffset, centerOffset, centerSize, centerSize), lightGreyPaint);
    canvas.drawRect(Rect.fromLTWH(centerOffset, centerOffset, centerSize, centerSize), blackPaint);

    // Draw triangles for each color pointing to the center
    final Path redTriangle = Path()
      ..moveTo(boardSize / 2, boardSize / 2)
      ..lineTo(centerOffset, centerOffset)
      ..lineTo(centerOffset + centerSize, centerOffset)
      ..close();

    final Path greenTriangle = Path()
      ..moveTo(boardSize / 2, boardSize / 2)
      ..lineTo(boardSize - centerOffset, centerOffset)
      ..lineTo(boardSize - centerOffset - centerSize, centerOffset)
      ..close();

    final Path yellowTriangle = Path()
      ..moveTo(boardSize / 2, boardSize / 2)
      ..lineTo(centerOffset, boardSize - centerOffset)
      ..lineTo(centerOffset + centerSize, boardSize - centerOffset)
      ..close();

    final Path blueTriangle = Path()
      ..moveTo(boardSize / 2, boardSize / 2)
      ..lineTo(boardSize - centerOffset, boardSize - centerOffset)
      ..lineTo(boardSize - centerOffset - centerSize, boardSize - centerOffset)
      ..close();

    canvas.drawPath(redTriangle, redPaint);
    canvas.drawPath(greenTriangle, greenPaint);
    canvas.drawPath(yellowTriangle, yellowPaint);
    canvas.drawPath(blueTriangle, bluePaint);

    // Draw home paths for each color
    _drawHomePaths(canvas, boardSize, cellSize, redPaint, greenPaint, bluePaint, yellowPaint, blackPaint);
  }

  /// Draw the main track (outer square)
  void _drawMainTrack(
    Canvas canvas,
    double boardSize,
    double cellSize,
    Paint whitePaint,
    Paint blackPaint,
    Paint safePaint,
    Paint starPaint,
  ) {
    // Draw the main track cells
    for (int i = 0; i < 3; i++) {
      for (int j = 0; j < 6; j++) {
        // Top row
        _drawCell(canvas, (j + 6) * cellSize, i * cellSize, cellSize, whitePaint, blackPaint);

        // Bottom row
        _drawCell(canvas, (j + 6) * cellSize, (14 - i) * cellSize, cellSize, whitePaint, blackPaint);

        // Left column
        _drawCell(canvas, i * cellSize, (j + 6) * cellSize, cellSize, whitePaint, blackPaint);

        // Right column
        _drawCell(canvas, (14 - i) * cellSize, (j + 6) * cellSize, cellSize, whitePaint, blackPaint);
      }
    }

    // Draw safe spots (stars) based on the reference image
    // These are the positions where tokens cannot be captured
    List<Offset> safeSpots = [
      // Star positions on the main track
      Offset(2 * cellSize, 7 * cellSize), // Red path star
      Offset(7 * cellSize, 2 * cellSize), // Green path star
      Offset(12 * cellSize, 7 * cellSize), // Blue path star
      Offset(7 * cellSize, 12 * cellSize), // Yellow path star
      // Additional stars in the center area
      Offset(5 * cellSize, 5 * cellSize), // Center area star
      Offset(9 * cellSize, 5 * cellSize), // Center area star
      Offset(5 * cellSize, 9 * cellSize), // Center area star
      Offset(9 * cellSize, 9 * cellSize), // Center area star
    ];

    for (var spot in safeSpots) {
      _drawSafeSpot(canvas, spot.dx, spot.dy, cellSize, safePaint, blackPaint);
    }
  }

  /// Draw a single cell on the board
  void _drawCell(Canvas canvas, double x, double y, double size, Paint fillPaint, Paint strokePaint) {
    final Rect cell = Rect.fromLTWH(x, y, size, size);
    canvas.drawRect(cell, fillPaint);
    canvas.drawRect(cell, strokePaint);
  }

  /// Draw a safe spot (star) on the board
  void _drawSafeSpot(Canvas canvas, double x, double y, double size, Paint fillPaint, Paint strokePaint) {
    final Rect cell = Rect.fromLTWH(x, y, size, size);
    canvas.drawRect(cell, fillPaint);
    canvas.drawRect(cell, strokePaint);

    // Draw a star shape
    final Path starPath = _createStarPath(x + size / 2, y + size / 2, size * 0.4, 8);
    canvas.drawPath(starPath, strokePaint);
  }

  /// Create a star path
  Path _createStarPath(double centerX, double centerY, double radius, int points) {
    final Path path = Path();
    final double angleStep = 2 * 3.14159 / points;

    for (int i = 0; i < points; i++) {
      final double angle = i * angleStep - 3.14159 / 2;
      final double x = centerX + radius * cos(angle);
      final double y = centerY + radius * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    path.close();
    return path;
  }

  /// Draw home paths for each color
  void _drawHomePaths(
    Canvas canvas,
    double boardSize,
    double cellSize,
    Paint redPaint,
    Paint greenPaint,
    Paint bluePaint,
    Paint yellowPaint,
    Paint blackPaint,
  ) {
    // Create paints with opacity for home paths
    final Paint redPathPaint = Paint()..color = redPaint.color.withAlpha(77);
    final Paint greenPathPaint = Paint()..color = greenPaint.color.withAlpha(77);
    final Paint bluePathPaint = Paint()..color = bluePaint.color.withAlpha(77);
    final Paint yellowPathPaint = Paint()..color = yellowPaint.color.withAlpha(77);

    // Home paths based on the reference image

    // Red home path (Computer 1) - from left to center
    for (int i = 1; i <= 5; i++) {
      _drawCell(canvas, (i + 1) * cellSize, 7 * cellSize, cellSize, redPathPaint, blackPaint);
    }

    // Green home path (Computer 2) - from top to center
    for (int i = 1; i <= 5; i++) {
      _drawCell(canvas, 7 * cellSize, (i + 1) * cellSize, cellSize, greenPathPaint, blackPaint);
    }

    // Blue home path (You) - from bottom to center
    for (int i = 1; i <= 5; i++) {
      _drawCell(canvas, 7 * cellSize, (13 - i) * cellSize, cellSize, bluePathPaint, blackPaint);
    }

    // Yellow home path (Computer 3) - from right to center
    for (int i = 1; i <= 5; i++) {
      _drawCell(canvas, (13 - i) * cellSize, 7 * cellSize, cellSize, yellowPathPaint, blackPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
