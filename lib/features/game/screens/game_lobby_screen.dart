import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ludo_flutter/utils/theme.dart';

class GameLobbyScreen extends StatefulWidget {
  const GameLobbyScreen({super.key});

  @override
  State<GameLobbyScreen> createState() => _GameLobbyScreenState();
}

class _GameLobbyScreenState extends State<GameLobbyScreen> {
  int _selectedPlayerCount = 4;
  bool _includeAI = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Game Lobby'),
        backgroundColor: AppTheme.cardColor,
        actions: [
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () {
              // TODO: Navigate to profile or settings
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Top section - Game options
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildGameOptions(),
                    const SizedBox(height: 24),
                    _buildActiveGames(),
                    const SizedBox(height: 24),
                    _buildLeaderboard(),
                  ],
                ),
              ),
            ),
            // Bottom section - Start game button
            _buildStartGameButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildGameOptions() {
    return Card(
      color: AppTheme.cardColor,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: AppTheme.primaryColor.withOpacity(0.3)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Game Options',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: AppTheme.accentColor),
            ).animate().fadeIn().slideX(begin: -0.2, end: 0),
            const SizedBox(height: 16),

            // Number of players
            Row(
              children: [
                Expanded(
                  child: Text('Number of Players:', style: TextStyle(color: Colors.white.withOpacity(0.9))),
                ),
                _buildPlayerNumberSelector(),
              ],
            ),
            const SizedBox(height: 16),

            // Include AI opponents
            Row(
              children: [
                Expanded(
                  child: Text('Include AI Opponents:', style: TextStyle(color: Colors.white.withOpacity(0.9))),
                ),
                Switch(
                  value: _includeAI,
                  activeColor: AppTheme.secondaryColor,
                  onChanged: (value) {
                    setState(() {
                      _includeAI = value;
                    });
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayerNumberSelector() {
    return Row(
      children: [
        _buildPlayerCountButton(2),
        const SizedBox(width: 8),
        _buildPlayerCountButton(3),
        const SizedBox(width: 8),
        _buildPlayerCountButton(4),
      ],
    );
  }

  Widget _buildPlayerCountButton(int count) {
    final isSelected = _selectedPlayerCount == count;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPlayerCount = count;
        });
      },
      child: Container(
        height: 36,
        width: 36,
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: isSelected ? AppTheme.primaryColor : Colors.white.withOpacity(0.3)),
        ),
        child: Center(
          child: Text(
            count.toString(),
            style: TextStyle(
              color: isSelected ? Colors.white : Colors.white.withOpacity(0.7),
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActiveGames() {
    return Card(
      color: AppTheme.cardColor,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: AppTheme.secondaryColor.withOpacity(0.3)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Active Games',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: AppTheme.secondaryColor),
                ).animate().fadeIn().slideX(begin: -0.2, end: 0),

                TextButton.icon(
                  icon: const Icon(Icons.refresh),
                  label: const Text('Refresh'),
                  onPressed: () {
                    // TODO: Refresh active games list
                  },
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Demo active games list (replace with real data later)
            _buildActiveGameItem('Game #2538', '2/4 players', '0.1 TON bet'),
            _buildActiveGameItem('Game #1897', '1/3 players', '0.5 TON bet'),
            _buildActiveGameItem('Game #3421', '2/4 players', 'Friendly match'),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveGameItem(String name, String players, String bet) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(color: Colors.black.withOpacity(0.3), borderRadius: BorderRadius.circular(8)),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(name, style: const TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 4),
                Text(players, style: TextStyle(fontSize: 12, color: Colors.white.withOpacity(0.7))),
              ],
            ),
          ),
          Text(
            bet,
            style: TextStyle(color: AppTheme.secondaryColor, fontWeight: FontWeight.w500),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: () {
              // TODO: Join this game
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.secondaryColor,
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: const Text('Join'),
          ),
        ],
      ),
    ).animate().fadeIn();
  }

  Widget _buildLeaderboard() {
    return Card(
      color: AppTheme.cardColor,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.amber.withOpacity(0.3)),
      ),
      child: InkWell(
        onTap: () {
          // TODO: Navigate to full leaderboard
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(Icons.emoji_events, color: Colors.amber, size: 32),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Leaderboard', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    Text(
                      'See who\'s at the top of the rankings',
                      style: TextStyle(fontSize: 12, color: Colors.white.withOpacity(0.7)),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.chevron_right),
            ],
          ),
        ),
      ),
    ).animate().fadeIn().slideY(begin: 0.2, end: 0);
  }

  Widget _buildStartGameButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        border: Border(top: BorderSide(color: Colors.white.withOpacity(0.1))),
      ),
      child: ElevatedButton(
        onPressed: _startNewGame,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.accentColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: const Text('Start New Game', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
      ),
    );
  }

  void _startNewGame() {
    // TODO: Handle game creation logic
    final gameInfo = {'playerCount': _selectedPlayerCount, 'includeAI': _includeAI};

    print('Creating new game with settings: $gameInfo');

    // Navigate to game board screen
    Navigator.pushNamed(context, '/game-board');
  }
}
