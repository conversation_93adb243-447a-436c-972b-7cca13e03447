import 'dart:math';

/// Represents the different player colors in the Ludo game
enum PlayerColor { red, green, blue, yellow }

/// Represents the different states a game can be in
enum GameState { waiting, active, completed, cancelled }

/// Represents the different states a token can be in
enum TokenState { home, inPlay, safe, finished }

/// Represents a player's token on the board
class Token {
  final PlayerColor color;
  final int id;
  TokenState state;
  int position;
  int steps;
  bool canMove;

  Token({
    required this.color,
    required this.id,
    this.state = TokenState.home,
    this.position = -1,
    this.steps = 0,
    this.canMove = false,
  });

  /// Check if the token can move with the given dice value
  bool canMoveWithDice(int diceValue) {
    // Can only leave home with a 6
    if (state == TokenState.home && diceValue != 6) {
      return false;
    }

    // If already in play, check if the move would exceed the finish line
    if (state == TokenState.inPlay) {
      // Each player has 57 steps to complete (51 on main track + 6 on home track)
      if (steps + diceValue > 57) {
        return false;
      }
    }

    return true;
  }

  /// Move the token by the given dice value
  void move(int diceValue) {
    if (state == TokenState.home && diceValue == 6) {
      // Move out of home
      state = TokenState.inPlay;
      steps = 0;
      position = getStartPosition(color);
    } else if (state == TokenState.inPlay) {
      // Move along the track
      steps += diceValue;
      position = calculatePosition(color, steps);

      // Check if token has reached the finish line
      if (steps == 57) {
        state = TokenState.finished;
      } else if (steps > 51) {
        // Token is on the home track
        state = TokenState.safe;
      }
    }
  }

  /// Get the starting position for a player color
  static int getStartPosition(PlayerColor color) {
    switch (color) {
      case PlayerColor.red:
        return 0;
      case PlayerColor.green:
        return 13;
      case PlayerColor.blue:
        return 26;
      case PlayerColor.yellow:
        return 39;
    }
  }

  /// Calculate the position on the board based on player color and steps
  static int calculatePosition(PlayerColor color, int steps) {
    int startPos = getStartPosition(color);

    // If on the main track (steps <= 51)
    if (steps <= 51) {
      return (startPos + steps) % 52;
    } else {
      // On the home track (steps > 51)
      // Each player has a different home track
      return 100 + (color.index * 10) + (steps - 51);
    }
  }

  /// Check if this token can capture another token
  bool canCapture(Token other) {
    // Can't capture tokens in safe spots or finished
    if (other.state == TokenState.safe || other.state == TokenState.finished || other.state == TokenState.home) {
      return false;
    }

    // Can't capture tokens of the same color
    if (color == other.color) {
      return false;
    }

    // Can only capture if on the same position on the main track
    return position == other.position && position < 100;
  }

  /// Reset the token to home
  void resetToHome() {
    state = TokenState.home;
    position = -1;
    steps = 0;
    canMove = false;
  }
}

/// Represents a player in the Ludo game
class Player {
  final PlayerColor color;
  final String name;
  final String address;
  final List<Token> tokens;
  bool isActive;
  bool hasWon;

  Player({
    required this.color,
    required this.name,
    required this.address,
    required this.tokens,
    this.isActive = false,
    this.hasWon = false,
  });

  /// Check if all tokens have reached the finish line
  bool get hasFinished => tokens.every((token) => token.state == TokenState.finished);

  /// Get the number of tokens at home
  int get tokensAtHome => tokens.where((token) => token.state == TokenState.home).length;

  /// Get the number of tokens that have finished
  int get tokensFinished => tokens.where((token) => token.state == TokenState.finished).length;

  /// Check if the player can move any token with the given dice value
  bool canMoveAnyToken(int diceValue) {
    return tokens.any((token) => token.canMoveWithDice(diceValue));
  }

  /// Update which tokens can move with the given dice value
  void updateMovableTokens(int diceValue) {
    for (var token in tokens) {
      token.canMove = token.canMoveWithDice(diceValue);
    }
  }
}

/// Main class for managing the Ludo game board and logic
class LudoBoard {
  final List<Player> players;
  GameState gameState;
  int currentPlayerIndex;
  int diceValue;
  String? gameId;
  String? contractAddress;

  LudoBoard({
    required this.players,
    this.gameState = GameState.waiting,
    this.currentPlayerIndex = 0,
    this.diceValue = 0,
    this.gameId,
    this.contractAddress,
  });

  /// Get the current player
  Player get currentPlayer => players[currentPlayerIndex];

  /// Roll the dice (local implementation)
  int rollDice() {
    final random = Random();
    diceValue = random.nextInt(6) + 1;
    return diceValue;
  }

  /// Roll the dice using the blockchain contract
  Future<int> rollDiceOnChain() async {
    try {
      // TODO: Implement contract interaction
      // This is a placeholder for the actual contract interaction
      await Future.delayed(const Duration(milliseconds: 500));
      return rollDice(); // Temporary fallback
    } catch (e) {
      print('Error rolling dice on chain: $e');
      return rollDice(); // Fallback to local implementation
    }
  }

  /// Move a token and handle game logic
  bool moveToken(int tokenId) {
    // Get the token to move
    final token = currentPlayer.tokens.firstWhere((t) => t.id == tokenId);

    // Check if the token can move
    if (!token.canMove) {
      return false;
    }

    // Move the token
    token.move(diceValue);

    // Check for captures
    _checkForCaptures(token);

    // Check if the player has won
    if (currentPlayer.hasFinished) {
      currentPlayer.hasWon = true;
      // Check if the game is over
      if (players.where((p) => !p.hasWon).length <= 1) {
        gameState = GameState.completed;
      }
    }

    // Determine if the player gets another turn (rolled a 6)
    bool getAnotherTurn = diceValue == 6;

    // Move to the next player if no extra turn
    if (!getAnotherTurn) {
      _nextPlayer();
    }

    return true;
  }

  /// Check if the token can capture any other tokens
  void _checkForCaptures(Token token) {
    for (var player in players) {
      if (player.color == token.color) continue; // Skip same color

      for (var otherToken in player.tokens) {
        if (token.canCapture(otherToken)) {
          otherToken.resetToHome();
        }
      }
    }
  }

  /// Move to the next player
  void _nextPlayer() {
    do {
      currentPlayerIndex = (currentPlayerIndex + 1) % players.length;
    } while (players[currentPlayerIndex].hasWon);

    // Update the current player's movable tokens
    currentPlayer.updateMovableTokens(diceValue);
  }

  /// Initialize a new game
  void startGame() {
    gameState = GameState.active;
    currentPlayerIndex = 0;

    // Reset all players and tokens
    for (var player in players) {
      player.hasWon = false;
      for (var token in player.tokens) {
        token.resetToHome();
      }
    }

    // Set the first player as active
    players[currentPlayerIndex].isActive = true;
  }
}
