import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:web3dart/web3dart.dart';
import 'ludo_board.dart';

class GameRules {
  // Ethereum contract addresses
  final String _diceContractAddress;
  final String _gameFactoryContractAddress;

  // Web3 client for Ethereum interaction
  final Web3Client? _web3client;

  // Ludo board instance for game logic
  LudoBoard? _ludoBoard;

  // Constructor
  GameRules({String diceContractAddress = '', String gameFactoryContractAddress = '', Web3Client? web3client})
    : _diceContractAddress = diceContractAddress,
      _gameFactoryContractAddress = gameFactoryContractAddress,
      _web3client = web3client;

  // Initialize the game with players
  void initializeGame(List<Player> players) {
    _ludoBoard = LudoBoard(players: players, contractAddress: _gameFactoryContractAddress);
  }

  // Roll the dice
  Future<int> rollDice() async {
    if (_web3client != null && _diceContractAddress.isNotEmpty) {
      try {
        // Try to roll dice on-chain
        return await _rollDiceOnChain();
      } catch (e) {
        if (kDebugMode) {
          print('Error rolling dice on chain: $e');
        }
        // Fallback to local dice roll
        return _rollDiceLocal();
      }
    } else {
      // Use local dice roll if no web3 client or contract address
      return _rollDiceLocal();
    }
  }

  // Roll dice on the blockchain
  Future<int> _rollDiceOnChain() async {
    if (_web3client == null || _diceContractAddress.isEmpty) {
      throw Exception('Web3 client or contract address not set');
    }

    try {
      // TODO: Implement actual contract interaction
      // This is a placeholder for the actual contract interaction
      await Future.delayed(const Duration(milliseconds: 500));
      return _rollDiceLocal(); // Temporary fallback
    } catch (e) {
      throw Exception('Failed to roll dice on chain: $e');
    }
  }

  // Roll dice locally
  int _rollDiceLocal() {
    return Random().nextInt(6) + 1;
  }

  // Move a token
  bool moveToken(int tokenId) {
    if (_ludoBoard == null) {
      return false;
    }

    return _ludoBoard!.moveToken(tokenId);
  }

  // Check if a player has won
  bool hasPlayerWon(PlayerColor color) {
    if (_ludoBoard == null) {
      return false;
    }

    final player = _ludoBoard!.players.firstWhere((p) => p.color == color, orElse: () => throw Exception('Player not found'));

    return player.hasWon;
  }

  // Get the current player
  Player? getCurrentPlayer() {
    if (_ludoBoard == null) {
      return null;
    }

    return _ludoBoard!.currentPlayer;
  }

  // Get the current dice value
  int getDiceValue() {
    if (_ludoBoard == null) {
      return 0;
    }

    return _ludoBoard!.diceValue;
  }

  // Start a new game
  void startGame() {
    if (_ludoBoard == null) {
      return;
    }

    _ludoBoard!.startGame();
  }
}
