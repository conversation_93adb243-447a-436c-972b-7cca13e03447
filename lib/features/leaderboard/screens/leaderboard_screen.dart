import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ludo_flutter/utils/theme.dart';

class LeaderboardScreen extends StatelessWidget {
  const LeaderboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Leaderboard'), backgroundColor: AppTheme.cardColor),
      body: Safe<PERSON><PERSON>(
        child: <PERSON>umn(
          children: [
            _buildLeaderboardHeader(),
            Expanded(child: _buildLeaderboardList()),
          ],
        ),
      ),
    );
  }

  Widget _buildLeaderboardHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), offset: const Offset(0, 2), blurRadius: 4)],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.emoji_events,
            color: Colors.amber,
            size: 48,
          ).animate().scale(duration: 600.ms, curve: Curves.elasticOut),
          const SizedBox(height: 8),
          const Text('Top Players', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
          const SizedBox(height: 4),
          Text('Compete to be the Ludo champion!', style: TextStyle(fontSize: 14, color: Colors.white.withOpacity(0.7))),
        ],
      ),
    );
  }

  Widget _buildLeaderboardList() {
    // This would typically be fetched from a service or API
    final List<Map<String, dynamic>> leaderboardData = [
      {'rank': 1, 'name': 'Pro Gamer', 'score': 3250, 'wins': 47, 'avatar': 'https://via.placeholder.com/48'},
      {'rank': 2, 'name': 'LudoMaster', 'score': 2930, 'wins': 41, 'avatar': 'https://via.placeholder.com/48'},
      {'rank': 3, 'name': 'TONChampion', 'score': 2745, 'wins': 38, 'avatar': 'https://via.placeholder.com/48'},
      {'rank': 4, 'name': 'GameWizard', 'score': 2510, 'wins': 36, 'avatar': 'https://via.placeholder.com/48'},
      {'rank': 5, 'name': 'LudoKing', 'score': 2350, 'wins': 33, 'avatar': 'https://via.placeholder.com/48'},
      {'rank': 6, 'name': 'BoardMaster', 'score': 2190, 'wins': 28, 'avatar': 'https://via.placeholder.com/48'},
      {'rank': 7, 'name': 'PieceMovers', 'score': 1950, 'wins': 24, 'avatar': 'https://via.placeholder.com/48'},
      {'rank': 8, 'name': 'DiceRoller', 'score': 1840, 'wins': 22, 'avatar': 'https://via.placeholder.com/48'},
      {'rank': 9, 'name': 'TONPlayer', 'score': 1720, 'wins': 20, 'avatar': 'https://via.placeholder.com/48'},
      {'rank': 10, 'name': 'LudoStrategy', 'score': 1680, 'wins': 19, 'avatar': 'https://via.placeholder.com/48'},
    ];

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: leaderboardData.length,
      itemBuilder: (context, index) {
        final player = leaderboardData[index];
        return _buildLeaderboardItem(
          rank: player['rank'],
          name: player['name'],
          score: player['score'],
          wins: player['wins'],
          avatarUrl: player['avatar'],
          isTopThree: index < 3,
        ).animate().fadeIn(delay: 100.ms * index, duration: 300.ms);
      },
    );
  }

  Widget _buildLeaderboardItem({
    required int rank,
    required String name,
    required int score,
    required int wins,
    required String avatarUrl,
    required bool isTopThree,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.05), blurRadius: 2, offset: const Offset(0, 1))],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: _buildRankIndicator(rank, isTopThree),
        title: Text(name, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
        subtitle: Text('$wins wins'),
        trailing: Text(
          '$score pts',
          style: TextStyle(color: AppTheme.accentColor, fontWeight: FontWeight.bold, fontSize: 16),
        ),
      ),
    );
  }

  Widget _buildRankIndicator(int rank, bool isTopThree) {
    final Color rankColor = isTopThree
        ? [Colors.amber, const Color(0xFFC0C0C0), const Color(0xFFCD7F32)][rank - 1]
        : Colors.grey.shade700;

    return Container(
      width: 36,
      height: 36,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: rankColor.withOpacity(0.2),
        border: Border.all(color: rankColor, width: 2),
      ),
      child: Center(
        child: Text(
          rank.toString(),
          style: TextStyle(color: rankColor, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}
