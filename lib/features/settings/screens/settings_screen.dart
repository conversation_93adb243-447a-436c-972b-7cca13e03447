import 'package:flutter/material.dart';
import 'package:ludo_flutter/services/audio.dart';
import 'package:ludo_flutter/services/auth_service.dart';
import 'package:ludo_flutter/utils/theme.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  // Settings state
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _notificationsEnabled = true;
  String _selectedTheme = 'Dark';
  String _selectedLanguage = 'English';

  // Audio service
  final _audioService = AudioService();

  // Auth service
  final _authService = AuthService();

  @override
  void initState() {
    super.initState();
    _soundEnabled = _audioService.isSoundEnabled;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Settings'), backgroundColor: AppTheme.cardColor),
      body: Safe<PERSON><PERSON>(
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildUserSection(),
            const SizedBox(height: 24),
            _buildGameSettings(),
            const SizedBox(height: 24),
            _buildAppSettings(),
            const SizedBox(height: 24),
            _buildTonWalletSection(),
            const SizedBox(height: 24),
            _buildAboutSection(),
            const SizedBox(height: 24),
            _buildSignOutButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: AppTheme.accentColor),
      ),
    );
  }

  Widget _buildSettingsCard(Widget child) {
    return Card(
      color: AppTheme.cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Padding(padding: const EdgeInsets.all(16), child: child),
    );
  }

  Widget _buildUserSection() {
    final user = _authService.currentUser;
    final isGuest = user?.id.startsWith('guest-') ?? true;

    return _buildSettingsCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('User Profile'),
          ListTile(
            contentPadding: EdgeInsets.zero,
            leading: CircleAvatar(
              backgroundColor: isGuest ? Colors.grey : AppTheme.primaryColor,
              child: Icon(isGuest ? Icons.person_outline : Icons.person, color: Colors.white),
            ),
            title: Text(user?.name ?? 'Guest User'),
            subtitle: Text(
              isGuest ? 'Playing as guest' : 'TON Wallet User',
              style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 12),
            ),
            trailing: TextButton(
              onPressed: () {
                // TODO: Navigate to profile edit screen
              },
              child: const Text('Edit'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameSettings() {
    return _buildSettingsCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Game Settings'),
          SwitchListTile(
            contentPadding: EdgeInsets.zero,
            title: const Text('Sound Effects'),
            subtitle: Text('Enable game sounds', style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 12)),
            value: _soundEnabled,
            activeColor: AppTheme.secondaryColor,
            onChanged: (value) {
              setState(() {
                _soundEnabled = value;
                _audioService.toggleSound();
              });
            },
          ),
          SwitchListTile(
            contentPadding: EdgeInsets.zero,
            title: const Text('Vibration'),
            subtitle: Text('Vibrate on events', style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 12)),
            value: _vibrationEnabled,
            activeColor: AppTheme.secondaryColor,
            onChanged: (value) {
              setState(() {
                _vibrationEnabled = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAppSettings() {
    return _buildSettingsCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('App Settings'),
          ListTile(
            contentPadding: EdgeInsets.zero,
            title: const Text('Theme'),
            subtitle: Text('Select app theme', style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 12)),
            trailing: DropdownButton<String>(
              value: _selectedTheme,
              dropdownColor: AppTheme.bgColor,
              items: const [
                DropdownMenuItem(value: 'Dark', child: Text('Dark')),
                DropdownMenuItem(value: 'Light', child: Text('Light')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedTheme = value;
                  });
                }
              },
            ),
          ),
          ListTile(
            contentPadding: EdgeInsets.zero,
            title: const Text('Language'),
            subtitle: Text('Select app language', style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 12)),
            trailing: DropdownButton<String>(
              value: _selectedLanguage,
              dropdownColor: AppTheme.bgColor,
              items: const [
                DropdownMenuItem(value: 'English', child: Text('English')),
                DropdownMenuItem(value: 'Spanish', child: Text('Spanish')),
                DropdownMenuItem(value: 'Russian', child: Text('Russian')),
              ],
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedLanguage = value;
                  });
                }
              },
            ),
          ),
          SwitchListTile(
            contentPadding: EdgeInsets.zero,
            title: const Text('Notifications'),
            subtitle: Text('Enable push notifications', style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 12)),
            value: _notificationsEnabled,
            activeColor: AppTheme.secondaryColor,
            onChanged: (value) {
              setState(() {
                _notificationsEnabled = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTonWalletSection() {
    final isWalletConnected = _authService.currentUser?.id.startsWith('ton-wallet') ?? false;

    return _buildSettingsCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('TON Wallet'),
          ListTile(
            contentPadding: EdgeInsets.zero,
            title: Text(isWalletConnected ? 'Wallet Connected' : 'Connect Wallet'),
            subtitle: Text(
              isWalletConnected ? 'Manage your TON wallet connection' : 'Connect your TON wallet to play with tokens',
              style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 12),
            ),
            trailing: isWalletConnected
                ? ElevatedButton(
                    onPressed: () {
                      // TODO: Disconnect wallet
                    },
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.redAccent),
                    child: const Text('Disconnect'),
                  )
                : ElevatedButton(
                    onPressed: () {
                      // TODO: Connect wallet
                    },
                    style: ElevatedButton.styleFrom(backgroundColor: AppTheme.primaryColor),
                    child: const Text('Connect'),
                  ),
          ),
          if (isWalletConnected)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: ListTile(
                contentPadding: EdgeInsets.zero,
                title: const Text('Wallet Balance'),
                subtitle: const Text('0.5 TON'),
                trailing: IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: () {
                    // TODO: Refresh balance
                  },
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAboutSection() {
    return _buildSettingsCard(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('About'),
          ListTile(
            contentPadding: EdgeInsets.zero,
            title: const Text('Privacy Policy'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // TODO: Navigate to privacy policy
            },
          ),
          ListTile(
            contentPadding: EdgeInsets.zero,
            title: const Text('Terms of Service'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // TODO: Navigate to terms of service
            },
          ),
          ListTile(contentPadding: EdgeInsets.zero, title: const Text('App Version'), trailing: const Text('1.0.0')),
        ],
      ),
    );
  }

  Widget _buildSignOutButton() {
    return ElevatedButton(
      onPressed: _handleSignOut,
      style: ElevatedButton.styleFrom(backgroundColor: Colors.redAccent, padding: const EdgeInsets.symmetric(vertical: 12)),
      child: const Text('Sign Out', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
    );
  }

  void _handleSignOut() async {
    // Show confirmation dialog
    final shouldSignOut = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(false), child: const Text('Cancel')),
          TextButton(onPressed: () => Navigator.of(context).pop(true), child: const Text('Sign Out')),
        ],
      ),
    );

    if (shouldSignOut == true) {
      await _authService.signOut();
      if (mounted) {
        Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
      }
    }
  }
}
