import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_vibrate/flutter_vibrate.dart';
import 'package:ludo_flutter/providers/auth_provider.dart';
import 'package:ludo_flutter/utils/theme.dart';

class LoginScreen extends ConsumerWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);

    // If already authenticated, navigate to game screen
    if (authState.isAuthenticated) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.pushReplacementNamed(context, '/game');
      });
    }

    return Scaffold(
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Logo and app name
                _buildHeader(),

                const SizedBox(height: 48),

                // Login form or buttons
                _buildLoginSection(context, ref),

                const SizedBox(height: 24),

                // Blockchain integration callout
                _buildBlockchainIntegration(),

                // Show loading indicator if authenticating
                if (authState.isLoading)
                  Padding(
                    padding: const EdgeInsets.only(top: 24),
                    child: Column(
                      children: [
                        const CircularProgressIndicator(),
                        const SizedBox(height: 16),
                        Text('Connecting to wallet...', style: TextStyle(color: Colors.white.withAlpha(180))),
                      ],
                    ),
                  ),

                // Show error if any
                if (authState.error != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 24),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(color: Colors.red.withAlpha(50), borderRadius: BorderRadius.circular(8)),
                      child: Text(
                        authState.error!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // App logo/icon
        Container(
          height: 120,
          width: 120,
          decoration: BoxDecoration(color: AppTheme.primaryColor.withAlpha(51), borderRadius: BorderRadius.circular(24)),
          child: Icon(Icons.casino, size: 64, color: AppTheme.primaryColor),
        ).animate().fadeIn(duration: 600.ms).scale(delay: 200.ms),

        const SizedBox(height: 24),

        // App name
        Text(
          'LUDO ETH',
          style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold, color: AppTheme.accentColor),
        ).animate().fadeIn(delay: 400.ms).slideY(begin: 0.3, end: 0),

        const SizedBox(height: 8),

        // Tagline
        Text(
          'Play Ludo on the Ethereum Blockchain',
          style: TextStyle(fontSize: 16, color: Colors.white.withAlpha(179)),
        ).animate().fadeIn(delay: 600.ms),
      ],
    );
  }

  Widget _buildLoginSection(BuildContext context, WidgetRef ref) {
    final authNotifier = ref.read(authProvider.notifier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Main action button
        ElevatedButton(
          onPressed: () => _handleConnectWallet(context, authNotifier),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.wallet),
              const SizedBox(width: 8),
              const Text('Connect with Ethereum Wallet', style: TextStyle(fontSize: 16)),
            ],
          ),
        ).animate().fadeIn(delay: 800.ms).slideX(begin: 0.3, end: 0),

        const SizedBox(height: 20),

        // Play as guest option
        OutlinedButton(
          onPressed: () => _handlePlayAsGuest(context, authNotifier),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            side: BorderSide(color: AppTheme.primaryColor),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
          child: const Text('Play as Guest', style: TextStyle(fontSize: 16)),
        ).animate().fadeIn(delay: 1000.ms).slideX(begin: -0.3, end: 0),
      ],
    );
  }

  Widget _buildBlockchainIntegration() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.secondaryColor.withAlpha(77)),
      ),
      child: Column(
        children: [
          Text(
            '🚀 Powered by Ethereum Blockchain',
            style: TextStyle(fontWeight: FontWeight.bold, color: AppTheme.secondaryColor),
          ),
          const SizedBox(height: 8),
          Text(
            'Secure gameplay, transparent transactions, and blockchain rewards!',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 12, color: Colors.white.withAlpha(179)),
          ),
        ],
      ),
    ).animate().fadeIn(delay: 1200.ms);
  }

  Future<void> _handleConnectWallet(BuildContext context, AuthNotifier authNotifier) async {
    try {
      // Vibrate for feedback
      Vibrate.feedback(FeedbackType.success);

      // Connect wallet
      await authNotifier.connectWallet();
    } catch (e) {
      // Show error if the widget is still mounted
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Failed to connect wallet: $e')));
      }
    }
  }

  Future<void> _handlePlayAsGuest(BuildContext context, AuthNotifier authNotifier) async {
    try {
      // Vibrate for feedback
      Vibrate.feedback(FeedbackType.light);

      // Sign in as guest
      await authNotifier.signInAsGuest();
    } catch (e) {
      // Show error if the widget is still mounted
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Failed to sign in as guest: $e')));
      }
    }
  }
}
