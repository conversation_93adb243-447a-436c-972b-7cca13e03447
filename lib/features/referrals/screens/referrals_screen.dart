import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ludo_flutter/utils/theme.dart';

class ReferralsScreen extends StatefulWidget {
  const ReferralsScreen({super.key});

  @override
  State<ReferralsScreen> createState() => _ReferralsScreenState();
}

class _ReferralsScreenState extends State<ReferralsScreen> {
  // Mock referral data
  final String _referralCode = 'LUDOTON123';
  final int _totalReferrals = 5;
  final double _totalRewards = 2.5; // TON

  // Mock referral history
  final List<Map<String, dynamic>> _referralHistory = [
    {'name': '<PERSON>', 'date': '2023-06-15', 'reward': 0.5, 'status': 'completed'},
    {'name': '<PERSON>', 'date': '2023-06-10', 'reward': 0.5, 'status': 'completed'},
    {'name': '<PERSON>', 'date': '2023-06-05', 'reward': 0.5, 'status': 'completed'},
    {'name': '<PERSON>', 'date': '2023-06-01', 'reward': 0.5, 'status': 'completed'},
    {'name': '<PERSON> K.', 'date': '2023-05-28', 'reward': 0.5, 'status': 'completed'},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Referrals'), backgroundColor: AppTheme.cardColor),
      body: SafeArea(
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            _buildReferralStats(),
            const SizedBox(height: 24),
            _buildInviteSection(),
            const SizedBox(height: 24),
            _buildReferralHistory(),
            const SizedBox(height: 24),
            _buildHowItWorks(),
          ],
        ),
      ),
    );
  }

  Widget _buildReferralStats() {
    return Card(
      color: AppTheme.cardColor,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: AppTheme.primaryColor.withOpacity(0.3)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Your Referral Stats',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: AppTheme.accentColor),
            ).animate().fadeIn().slideX(begin: -0.2, end: 0),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(child: _buildStatItem('Total Referrals', _totalReferrals.toString(), Icons.people)),
                Expanded(child: _buildStatItem('Total Rewards', '$_totalRewards TON', Icons.wallet)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: AppTheme.secondaryColor, size: 32).animate().scale(delay: 300.ms),
        const SizedBox(height: 8),
        Text(value, style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
        const SizedBox(height: 4),
        Text(label, style: TextStyle(fontSize: 14, color: Colors.white.withOpacity(0.7))),
      ],
    );
  }

  Widget _buildInviteSection() {
    return Card(
      color: AppTheme.cardColor,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: AppTheme.secondaryColor.withOpacity(0.3)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Invite Friends',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: AppTheme.secondaryColor),
            ).animate().fadeIn().slideX(begin: -0.2, end: 0),
            const SizedBox(height: 16),

            Text(
              'Share your referral code with friends and earn rewards when they sign up and play!',
              style: TextStyle(color: Colors.white.withOpacity(0.7)),
            ),
            const SizedBox(height: 16),

            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.white.withOpacity(0.1)),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      _referralCode,
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, letterSpacing: 1),
                    ),
                  ),
                  IconButton(icon: const Icon(Icons.copy), onPressed: () => _copyReferralCode(), tooltip: 'Copy to clipboard'),
                ],
              ),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.share),
                    label: const Text('Share Link'),
                    onPressed: () => _shareReferralLink(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.secondaryColor,
                      foregroundColor: Colors.black,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReferralHistory() {
    return Card(
      color: AppTheme.cardColor,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: AppTheme.accentColor.withOpacity(0.3)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Referral History',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: AppTheme.accentColor),
            ).animate().fadeIn().slideX(begin: -0.2, end: 0),
            const SizedBox(height: 16),

            if (_referralHistory.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      const Icon(Icons.people_outline, size: 48, color: Colors.grey),
                      const SizedBox(height: 16),
                      Text('No referrals yet', style: TextStyle(color: Colors.white.withOpacity(0.7))),
                    ],
                  ),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _referralHistory.length,
                itemBuilder: (context, index) {
                  final referral = _referralHistory[index];
                  return _buildReferralItem(
                    name: referral['name'],
                    date: referral['date'],
                    reward: referral['reward'],
                    status: referral['status'],
                  ).animate().fadeIn(delay: 100.ms * index, duration: 300.ms);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildReferralItem({required String name, required String date, required double reward, required String status}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(color: Colors.black.withOpacity(0.2), borderRadius: BorderRadius.circular(8)),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: AppTheme.primaryColor.withOpacity(0.2),
            child: const Icon(Icons.person, color: Colors.white),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(name, style: const TextStyle(fontWeight: FontWeight.bold)),
                Text('Joined: $date', style: TextStyle(fontSize: 12, color: Colors.white.withOpacity(0.7))),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '$reward TON',
                style: TextStyle(color: AppTheme.secondaryColor, fontWeight: FontWeight.bold),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: status == 'completed' ? Colors.green.withOpacity(0.2) : Colors.orange.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  status == 'completed' ? 'Completed' : 'Pending',
                  style: TextStyle(fontSize: 10, color: status == 'completed' ? Colors.green : Colors.orange),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHowItWorks() {
    return Card(
      color: AppTheme.cardColor,
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'How It Works',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.white),
            ),
            const SizedBox(height: 16),

            _buildHowItWorksStep(
              step: '1',
              title: 'Share Your Code',
              description: 'Share your unique referral code with friends using the share button above.',
              icon: Icons.share,
            ),
            const SizedBox(height: 16),

            _buildHowItWorksStep(
              step: '2',
              title: 'Friend Signs Up',
              description: 'When your friend uses your code during signup, they get 0.2 TON bonus.',
              icon: Icons.person_add,
            ),
            const SizedBox(height: 16),

            _buildHowItWorksStep(
              step: '3',
              title: 'You Get Rewarded',
              description: 'You earn 0.5 TON for each friend who signs up and plays their first game!',
              icon: Icons.wallet_giftcard,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHowItWorksStep({
    required String step,
    required String title,
    required String description,
    required IconData icon,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(shape: BoxShape.circle, color: AppTheme.primaryColor),
          child: Center(
            child: Text(
              step,
              style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(icon, color: AppTheme.secondaryColor, size: 20),
                  const SizedBox(width: 8),
                  Text(title, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                ],
              ),
              const SizedBox(height: 4),
              Text(description, style: TextStyle(color: Colors.white.withOpacity(0.7), fontSize: 14)),
            ],
          ),
        ),
      ],
    );
  }

  void _copyReferralCode() {
    Clipboard.setData(ClipboardData(text: _referralCode));

    // Show snackbar
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Referral code copied to clipboard'), duration: Duration(seconds: 2)));
  }

  void _shareReferralLink() {
    // TODO: Implement share functionality
    // Would typically use a share plugin like share_plus

    // For now, just show a snackbar
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Sharing referral link...'), duration: Duration(seconds: 2)));
  }
}
