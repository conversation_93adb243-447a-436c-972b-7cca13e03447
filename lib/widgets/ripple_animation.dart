import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// A ripple animation effect using flutter_animate package
class RippleAnimation extends StatelessWidget {
  final Color color;
  final double minRadius;
  final bool repeat;
  final int ripplesCount;
  final Widget child;

  const RippleAnimation({
    super.key,
    required this.color,
    this.minRadius = 20,
    this.repeat = true,
    this.ripplesCount = 3,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Generate multiple ripple effects
        ...List.generate(ripplesCount, (index) {
          // Calculate staggered delay
          final delayMs = (index * 400 / ripplesCount).round();

          return Container(
                width: minRadius * 2,
                height: minRadius * 2,
                decoration: BoxDecoration(shape: BoxShape.circle, color: color),
              )
              .animate(onPlay: (controller) => repeat ? controller.repeat() : null)
              .scaleXY(begin: 1.0, end: 2.5, curve: Curves.easeOut, duration: 1500.ms, delay: delayMs.ms)
              .fadeOut(duration: 1500.ms, delay: delayMs.ms, curve: Curves.easeOut);
        }),

        // Child widget
        child,
      ],
    );
  }
}
