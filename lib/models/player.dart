import 'package:ludo_flutter/constants/constants.dart';

enum PlayerColor { red, green, blue, yellow }

/// Represents the different states a token can be in
enum TokenState { home, inPlay, safe, finished }

/// Represents a player's token on the board
class Token {
  final PlayerColor color;
  final int id;
  TokenState state;
  int position;
  int steps;
  bool canMove;

  Token({
    required this.color,
    required this.id,
    this.state = TokenState.home,
    this.position = -1,
    this.steps = 0,
    this.canMove = false,
  });

  /// Check if the token can move with the given dice value
  bool canMoveWithDice(int diceValue) {
    // Can only leave home with a 6
    if (state == TokenState.home && diceValue != 6) {
      return false;
    }

    // If already in play, check if the move would exceed the finish line
    if (state == TokenState.inPlay) {
      // Each player has 57 steps to complete (51 on main track + 6 on home track)
      if (steps + diceValue > 57) {
        return false;
      }
    }

    return true;
  }

  /// Reset the token to home
  void resetToHome() {
    state = TokenState.home;
    position = -1;
    steps = 0;
    canMove = false;
  }
}

/// Represents a player in the Ludo game
class Player {
  final String id;
  final String name;
  final PlayerColor color;
  final bool isComputer;
  final String address;
  final List<Token> tokens;
  bool hasWon;
  bool isActive;

  Player({
    required this.id,
    required this.name,
    required this.color,
    this.isComputer = false,
    this.address = '',
    List<Token>? tokens,
    this.hasWon = false,
    this.isActive = false,
  }) : tokens = tokens ?? List.generate(4, (index) => Token(id: index, color: color, state: TokenState.home));

  /// Get the default name for a player based on their color
  static String getDefaultNameForColor(PlayerColor color) {
    switch (color) {
      case PlayerColor.red:
        return LudoPlayerNames.computer1;
      case PlayerColor.green:
        return LudoPlayerNames.computer2;
      case PlayerColor.blue:
        return LudoPlayerNames.you;
      case PlayerColor.yellow:
        return LudoPlayerNames.computer3;
    }
  }

  /// Get the number of tokens at home
  int get tokensAtHome => tokens.where((token) => token.state == TokenState.home).length;

  /// Get the number of tokens that have finished
  int get tokensFinished => tokens.where((token) => token.state == TokenState.finished).length;

  /// Check if all tokens have reached the finish line
  bool get hasFinished => tokens.every((token) => token.state == TokenState.finished);

  /// Check if the player can move any token with the given dice value
  bool canMoveAnyToken(int diceValue) {
    return tokens.any((token) => token.canMoveWithDice(diceValue));
  }

  /// Update which tokens can move with the given dice value
  void updateMovableTokens(int diceValue) {
    for (var token in tokens) {
      token.canMove = token.canMoveWithDice(diceValue);
    }
  }
}
