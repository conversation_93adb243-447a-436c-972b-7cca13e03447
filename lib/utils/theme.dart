import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Define our theme colors
  static const Color primaryColor = Color(0xFF6D28D9); // Purple
  static const Color secondaryColor = Color(0xFF2DD4BF); // Teal
  static const Color accentColor = Color(0xFFFF7C7C); // Coral
  static const Color bgColor = Color(0xFF0F172A); // Very dark blue
  static const Color cardColor = Color(0xFF1E293B); // Dark blue-gray

  // Get the main app theme
  static ThemeData darkTheme(BuildContext context) {
    // Create a custom dark theme with our colors
    final ThemeData baseTheme = FlexThemeData.dark(
      scheme: FlexScheme.custom,
      primary: primaryColor,
      primaryLightRef: primaryColor, // Add reference to light mode primary
      secondary: secondaryColor,
      secondaryLightRef: secondaryColor, // Add reference to light mode secondary
      tertiary: accentColor,
      tertiaryLightRef: accentColor, // Add reference to light mode tertiary
      surface: bgColor,
      scaffoldBackground: bgColor,
      surfaceMode: FlexSurfaceMode.highScaffoldLowSurface,
      blendLevel: 20,
      appBarOpacity: 0.95,
      subThemesData: const FlexSubThemesData(
        blendOnLevel: 20,
        blendOnColors: false,
        cardElevation: 4,
        dialogElevation: 8,
        bottomSheetRadius: 20,
      ),
      visualDensity: FlexColorScheme.comfortablePlatformDensity,
      useMaterial3: true,
    );

    // Add Google Fonts
    final textTheme = GoogleFonts.poppinsTextTheme(baseTheme.textTheme);

    // Return the final merged theme
    return baseTheme.copyWith(textTheme: textTheme, cardColor: cardColor);
  }
}
